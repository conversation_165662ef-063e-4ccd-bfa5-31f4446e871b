import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  createDetail,
  getDetailsByCategory,
  getDetailById,
  updateDetail,
  deleteDetail,
  createSubDetail,
  getSubDetailsByDetail,
  getSubDetailById,
  updateSubDetail,
  deleteSubDetail,
  createValue,
  getValuesBySubDetail,
  getValueById,
  updateValue,
  deleteValue,

} from '../services/constantService';

// ConstantCategory Controllers
export const createCategoryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const category = await createCategory(req.body);
    return sendSuccess(res, category, 'Category created successfully', 201);
  } catch {
    return sendError(res, 'Failed to create category', 500);
  }
};

export const getAllCategoriesController = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await getAllCategories(page, limit, search);
    return sendSuccess(res, result, 'Categories retrieved successfully');
  } catch {
    return sendError(res, 'Failed to retrieve categories', 500);
  }
};

export const getCategoryByIdController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const category = await getCategoryById(id);
    
    if (!category) {
      return sendError(res, 'Category not found', 404);
    }
    
    return sendSuccess(res, category, 'Category retrieved successfully');
  } catch {
    return sendError(res, 'Category not found', 404);
  }
};

export const updateCategoryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const category = await updateCategory(id, req.body);
    return sendSuccess(res, category, 'Category updated successfully');
  } catch {
    return sendError(res, 'Failed to update category', 500);
  }
};

export const deleteCategoryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    await deleteCategory(id);
    return sendSuccess(res, null, 'Category deleted successfully');
  } catch {
    return sendError(res, 'Cannot delete category as it contains details or is being used', 500);
  }
};

// ConstantDetail Controllers
export const createDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const detail = await createDetail(req.body);
    return sendSuccess(res, detail, 'Detail created successfully', 201);
  } catch {
    return sendError(res, 'Failed to create detail', 500);
  }
};

export const getDetailsByCategoryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { categoryId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await getDetailsByCategory(categoryId, page, limit, search);
    return sendSuccess(res, result, 'Details retrieved successfully');
  } catch {
    return sendError(res, 'Failed to retrieve details', 500);
  }
};

export const getDetailByIdController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const detail = await getDetailById(id);
    
    if (!detail) {
      return sendError(res, 'Detail not found', 404);
    }
    
    return sendSuccess(res, detail, 'Detail retrieved successfully');
  } catch {
    return sendError(res, 'Detail not found', 404);
  }
};

export const updateDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const detail = await updateDetail(id, req.body);
    return sendSuccess(res, detail, 'Detail updated successfully');
  } catch {
    return sendError(res, 'Failed to update detail', 500);
  }
};

export const deleteDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    await deleteDetail(id);
    return sendSuccess(res, null, 'Detail deleted successfully');
  } catch {
    return sendError(res, 'Cannot delete detail as it contains sub-details or is being used', 500);
  }
};

// ConstantSubDetail Controllers
export const createSubDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const subDetail = await createSubDetail(req.body);
    return sendSuccess(res, subDetail, 'Sub-detail created successfully', 201);
  } catch {
    return sendError(res, 'Failed to create sub-detail', 500);
  }
};

export const getSubDetailsByDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { detailId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await getSubDetailsByDetail(detailId, page, limit, search);
    return sendSuccess(res, result, 'Sub-details retrieved successfully');
  } catch {
    return sendError(res, 'Failed to retrieve sub-details', 500);
  }
};

export const getSubDetailByIdController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const subDetail = await getSubDetailById(id);
    
    if (!subDetail) {
      return sendError(res, 'Sub-detail not found', 404);
    }
    
    return sendSuccess(res, subDetail, 'Sub-detail retrieved successfully');
  } catch {
    return sendError(res, 'Sub-detail not found', 404);
  }
};

export const updateSubDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const subDetail = await updateSubDetail(id, req.body);
    return sendSuccess(res, subDetail, 'Sub-detail updated successfully');
  } catch {
    return sendError(res, 'Failed to update sub-detail', 500);
  }
};

export const deleteSubDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    await deleteSubDetail(id);
    return sendSuccess(res, null, 'Sub-detail deleted successfully');
  } catch {
    return sendError(res, 'Cannot delete sub-detail as it contains values or is being used', 500);
  }
};

// ConstantSubDetailValue Controllers
export const createValueController = async (req: Request, res: Response): Promise<any> => {
  try {
    const value = await createValue(req.body);
    return sendSuccess(res, value, 'Value created successfully', 201);
  } catch {
    return sendError(res, 'Failed to create value', 500);
  }
};

export const getValuesBySubDetailController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { subDetailId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await getValuesBySubDetail(subDetailId, page, limit, search);
    return sendSuccess(res, result, 'Values retrieved successfully');
  } catch {
    return sendError(res, 'Failed to retrieve values', 500);
  }
};

export const getValueByIdController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const value = await getValueById(id);

    if (!value) {
      return sendError(res, 'Value not found', 404);
    }

    return sendSuccess(res, value, 'Value retrieved successfully');
  } catch {
    return sendError(res, 'Value not found', 404);
  }
};

export const updateValueController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const value = await updateValue(id, req.body);
    return sendSuccess(res, value, 'Value updated successfully');
  } catch {
    return sendError(res, 'Failed to update value', 500);
  }
};

export const deleteValueController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    await deleteValue(id);
    return sendSuccess(res, null, 'Value deleted successfully');
  } catch {
    return sendError(res, 'Cannot delete value as it may be in use by other records', 500);
  }
};


