import { Request, Response } from 'express';
import { addUestCoinTranscation, updateOrCreateUestCoins } from '../services/uestCoinTransactionService';

export const addUestCoinTranscationController = async (req: Request, res: Response): Promise<any> => {
    try {
        const data = req.body;
        const addUestCoinTranscationData = await addUestCoinTranscation(data);
        return res.status(200).json({
            success: true,
            data: addUestCoinTranscationData
        });
    } catch (error) {
        console.error('Error in addUestCoinTranscationController:', error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
    }
}


export const updateUestCoinsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { modelId, modelType, coins } = req.body;
    if (!modelId || !modelType || coins === undefined) {
      return res.status(400).json({
        success: false,
        error: "modelId, modelType, and coins are required",
      });
    }
    
    const updatedCoins = await updateOrCreateUestCoins(modelId, modelType, coins);
    return res.status(200).json({
      success: true,
      data: updatedCoins,
    });
  } catch (error) {
    console.error("Error in updateUestCoinsController:", error);
    return res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
};

