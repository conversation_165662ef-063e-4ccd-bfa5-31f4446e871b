import { UestCoinTransaction } from "@prisma/client";
import prisma from "../../../config/prismaClient";

export const addUestCoinTranscation = async (data:UestCoinTransaction) =>{
    const addUestCoinTranscation = await prisma.uestCoinTransaction.create({
        data
    });
    return addUestCoinTranscation;
}

export const getUestCoins = async (modelId: string, modelType: string) => {
  try {
    const uestCoins = await prisma.uestCoins.findUnique({
      where: {
        modelId_modelType: {
          modelId,
          modelType,
        },
      },
    });
    return uestCoins;
  } catch {
    throw new Error("Failed to fetch coins");
  }
};


export const updateOrCreateUestCoins = async (
  modelId: string,
  modelType: string,
  coinsToAdd: number
) => {
  try {
    const existingCoins = await getUestCoins(modelId, modelType);
    
    if (existingCoins) {
      // Update existing coin balance
      const updatedCoins = await prisma.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId,
            modelType,
          },
        },
        data: {
          coins: existingCoins.coins + coinsToAdd,
        },
      });
      return updatedCoins;
    } else {
      const newCoins = await prisma.uestCoins.create({
        data: {
          modelId,
          modelType,
          coins: coinsToAdd,
        },
      });
      return newCoins;
    }
  } catch {
    throw new Error("Failed to update or create coins");
  }
};