"use client";

import { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  getAdminNotifications,
  getAdminUnreadCount,
  markAdminNotificationAsRead,
  markAllAdminNotificationsAsRead,
  deleteAllAdminNotifications,
  getNotificationActionUrl,
  getNotificationIcon,
  AdminNotification
} from '@/services/notificationService';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';

export default function AdminNotificationBell() {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const router = useRouter();

  const safeNotifications = Array.isArray(notifications) ? notifications : [];

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const [result, count] = await Promise.all([
        getAdminNotifications(1, 20), 
        getAdminUnreadCount()
      ]);

      let notificationsList: AdminNotification[] = [];

      if (Array.isArray(result)) {
        notificationsList = result;
      } else if (result?.notifications && Array.isArray(result.notifications)) {
        notificationsList = result.notifications;
      } else {
        notificationsList = [];
      }



      setNotifications(notificationsList);
      setUnreadCount(count || 0);
    } catch (error) {
      console.error('Error fetching admin notifications:', error);
      toast.error('Failed to fetch notifications');
      // Set empty arrays on error
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAdminNotificationAsRead(notificationId);

      // Update local state
      setNotifications(prev => {
        if (!Array.isArray(prev)) return [];
        return prev.map(notif =>
          notif.id === notificationId ? { ...notif, isRead: true } : notif
        );
      });
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAdminNotificationsAsRead();

      // Update local state
      setNotifications(prev => {
        if (!Array.isArray(prev)) return [];
        return prev.map(notif => ({ ...notif, isRead: true }));
      });
      setUnreadCount(0);
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  const handleRemoveAllClick = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmRemoveAll = async () => {
    setShowDeleteDialog(false);

    try {
      await deleteAllAdminNotifications();

      // Update local state
      setNotifications([]);
      setUnreadCount(0);
      toast.success('All notifications removed successfully');
    } catch (error) {
      console.error('Error removing all notifications:', error);
      toast.error('Failed to remove all notifications');
    }
  };

  const handleNotificationClick = async (notification: AdminNotification) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      await handleMarkAsRead(notification.id);
    }

    // Navigate to relevant page
    const actionUrl = getNotificationActionUrl(notification);
    setIsOpen(false);
    router.push(actionUrl);
  };

  useEffect(() => {
    fetchNotifications();

    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="relative group border-orange-500 hover:border-orange-400 bg-black rounded-full"
        >
          <div className="absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity" />
          <div className="relative z-10">
            <Bell className="h-5 w-5 text-orange-500" />
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-96 p-0" align="end">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Admin Notifications</h3>
            <div className="flex gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  className="text-xs"
                >
                  Mark all read
                </Button>
              )}
              {safeNotifications.length > 0 && unreadCount === 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveAllClick}
                  className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  Remove all
                </Button>
              )}
            </div>
          </div>
        </div>
        <div className="h-96 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-muted-foreground">
              Loading notifications...
            </div>
          ) : safeNotifications.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              No notifications yet
            </div>
          ) : (
            <div className="divide-y">
              {safeNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${
                    !notification.isRead ? 'bg-blue-50/50' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3">
                    <div className="text-lg">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm">{notification.title}</p>
                        {!notification.isRead && (
                          <div className="w-2 h-2 rounded-full bg-blue-500" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        {safeNotifications.length > 0 && (
          <div className="p-3 border-t bg-muted/30">
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-xs"
              onClick={() => {
                setIsOpen(false);
                router.push('/notifications');
              }}
            >
              View All Notifications
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to remove all notifications? This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirmRemoveAll}
            className="bg-red-600 hover:bg-red-700"
          >
            Remove All
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </>
  );
}
