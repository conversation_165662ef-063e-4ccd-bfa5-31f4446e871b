'use client';

import * as React from 'react';
import {
  ActivitySquareIcon,
  ClipboardListIcon,
  LayoutDashboardIcon,
  UserCheck,
  Brain,
  Star,
  MessageSquareText,
  Notebook,
  Share2,
  Settings,
  FolderTree,
  PenSquare,
  Camera,
  MessageSquareTextIcon,
  Bell,
} from 'lucide-react';

import { NavMain } from '@/app-components/nav-main';
import { NavUser } from '@/app-components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import Image from 'next/image';

const data = {
  user: {
    name: 'uest admin',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  navMain: [
    {
      title: 'Classes',
      icon: LayoutDashboardIcon,
      children: [
        { title: 'Classes List', url: '/dashboard', icon: LayoutDashboardIcon },
        { title: 'Testimonials', url: '/testimonials', icon: MessageSquareText },
        { title: 'Thoughts', url: '/classes-thoughts', icon: Brain },
        { title: 'Blogs', url: '/blog', icon: Notebook },

      ]
    },
    {
      title: 'Students',
      icon: UserCheck,
      children: [
        { title: 'Student Details', url: '/student-details', icon: UserCheck },
        { title: 'Reviews', url: '/reviews', icon: Star },
      ]
    },
    {
      title: 'Exam',
      icon: ClipboardListIcon,
      children: [
        { title: 'Exam Details', url: '/exam-detail', icon: ClipboardListIcon },
        { title: 'Question Bank', url: '/question-bank', icon: ActivitySquareIcon },
        { title: 'Photo Monitoring', url: '/exam-monitoring', icon: Camera },
        {title: 'Mock Exam Question Bank', url: '/mock-question-bank', icon: PenSquare},
      ]
    },
    {
      title: 'Referrals',
      icon: Share2,
      children: [
        { title: 'Referral Management', url: '/referral-management', icon: Share2 },
      ]
    },
    {
      title: 'Constants',
      icon: Settings,
      children: [
        { title: 'Manage Categories', url: '/constants', icon: FolderTree },
      ]
    },
    {
      title: 'Chats',
      icon: MessageSquareTextIcon,
      children: [
        { title: 'Chats', url: '/chat', icon: MessageSquareTextIcon },
      ]
    },
    {
      title: 'Notifications',
      icon: Bell,
      children: [
        { title: 'All Notifications', url: '/notifications', icon: Bell },
      ]
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <a href="#">
                <Image
                  src="/logo.jpeg"
                  alt="App Logo"
                  width={130}
                  height={40}
                  className="rounded-md"
                />
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
