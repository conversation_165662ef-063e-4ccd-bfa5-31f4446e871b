'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSelector, useDispatch } from 'react-redux';
import { ExamFormValues, examSchema } from '@/lib/validations/examSchema';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { createExamAsync, updateExamAsync, closeFormDialog, fetchExams } from '@/app/examSlice';
import { AppDispatch, RootState } from '../app/store';

export default function ExamForm() {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedExam, currentPage, limit } = useSelector((state: RootState) => state.exam);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const today = new Date().toISOString().slice(0, 16);

  const form = useForm<ExamFormValues>({
    resolver: zodResolver(examSchema),
    defaultValues: selectedExam
      ? {
          exam_name: selectedExam.exam_name,
          start_date: new Date(selectedExam.start_date)
            .toLocaleString('sv-SE', { timeZone: 'Asia/Kolkata' })
            .replace(' ', 'T')
            .slice(0, 16),
          duration: selectedExam.duration,
          marks: Number(selectedExam.marks),
          total_student_intake: selectedExam.total_student_intake,
          total_questions: selectedExam.total_questions,
          level: selectedExam.level,
          coins_required: selectedExam.coins_required,
          exam_type: selectedExam.exam_type,
         start_registration_date: selectedExam.start_registration_date
          ? new Date(selectedExam.start_registration_date)
              .toLocaleString('sv-SE', { timeZone: 'Asia/Kolkata' })
              .replace(' ', 'T')
              .slice(0, 16)
          : undefined,
        }
      : {
          exam_name: '',
          start_date: '',
          duration: 0,
          marks: 0,
          total_student_intake: 0,
          total_questions: 0,
          level: 'easy',
          exam_type: 'CLASSES',
          coins_required: 0,
          start_registration_date: undefined,
        },
  });

  const onSubmit = async (values: ExamFormValues) => {
    setIsSubmitting(true);
    try {
      console.log('Form Data Sent to Backend:', values);
      let result;
      if (selectedExam) {
        result = await dispatch(updateExamAsync({ id: selectedExam.id, values })).unwrap();
        console.log('Update Exam Response:', result); 
        toast.success('Exam updated successfully!');
      } else {
        result = await dispatch(createExamAsync(values)).unwrap();
        console.log('Create Exam Response:', result);
        toast.success('Exam created successfully!');
        form.reset();
      }
      const exams = await dispatch(fetchExams({ page: currentPage, limit })).unwrap();
      console.log('Fetched Exams:', exams); 
      dispatch(closeFormDialog());
    } catch (error: any) {
      console.error('Error submitting form:', error);
      toast.error(error.message || 'Failed to submit exam. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="exam_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Exam Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter exam name"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="start_date"
              render={({ field }) => {
                const now = new Date();
                const isoString = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                  .toISOString()
                  .slice(0, 16);

                return (
                  <FormItem>
                    <FormLabel className="text-foreground">Start Date</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        min={isoString}
                        className="bg-background text-foreground border-input focus:border-primary"
                      />
                    </FormControl>
                    <FormMessage className="text-destructive" />
                  </FormItem>
                );
              }}
            />

            {/* Input for Starting Registration */}
             <FormField
              control={form.control}
              name="start_registration_date"
              render={({ field }) => {
                const now = new Date();
                const isoString = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                  .toISOString()
                  .slice(0, 16);

                return (
                  <FormItem>
                    <FormLabel className="text-foreground">Start Apply Date</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        min={isoString}
                        className="bg-background text-foreground border-input focus:border-primary"
                      />
                    </FormControl>
                    <FormMessage className="text-destructive" />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Duration (minutes)</FormLabel>
                  <FormControl>
                    <Input
                      min={today}
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      placeholder="Enter duration"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
        

            {/* Input for exam type */}
            <FormField
              control={form.control}
              name="exam_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Exam Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="bg-background text-foreground border-input focus:border-primary w-full">
                        <SelectValue placeholder="Select Exam Type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-background text-foreground border-input">
                      <SelectItem value="CLASSES">Classes</SelectItem>
                      <SelectItem value="STUDENTS">Students</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
          </div>

          

          {/* Right Column */}
          <div className="space-y-4">
              <FormField
              control={form.control}
              name="marks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Marks</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value ?? ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === '' ? 0 : Number(value));
                      }}
                      placeholder="Enter marks"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="total_student_intake"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Total Students</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      placeholder="Enter total students"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="total_questions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Total Questions</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      placeholder="Enter total questions"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />

 <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Level</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="bg-background text-foreground border-input focus:border-primary w-full">
                        <SelectValue placeholder="Select level" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-background text-foreground border-input">
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
            
            {/* <FormField
              control={form.control}
              name="max_questions_class_can_apply"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">Max Questions per Class</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value}
                      onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      placeholder="Enter max questions"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_classes_can_join"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max Classes Can Join</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value}
                      onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      placeholder="Max classes can join"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            /> */}

            <FormField
              control={form.control}
              name="coins_required"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Coin Deduction Value</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      value={field.value ?? 0}
                      onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      placeholder="Coin Deduction Value"
                      className="bg-background text-foreground border-input focus:border-primary"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Button
          type="submit"
          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
          disabled={isSubmitting}
        >
          {selectedExam ? 'Update' : 'Create'} Exam {isSubmitting && 'ing...'}
        </Button>
      </form>
    </Form>
  );
}
