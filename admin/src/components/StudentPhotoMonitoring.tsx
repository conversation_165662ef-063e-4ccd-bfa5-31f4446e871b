"use client";

import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Camera,
  Search,
  Users,
  Calendar,
  Download,
  X,
  RefreshCw,
} from "lucide-react";
import { DataTable } from "@/app-components/dataTable";
import { ColumnDef } from "@tanstack/react-table";
import { getExamApplicant } from "@/services/uwhizExamApplicantApi";
import { getStudentExamPhotos, ExamPhoto } from "@/services/examMonitoringApi";
import { getExams } from "@/services/examApi";
import { Applicant } from "@/lib/types";
import { toast } from "sonner";
import Pagination from '@/app-components/pagination';


interface Exam {
  id: number;
  exam_name: string;
  start_date: string;
}

const StudentPhotoMonitoring: React.FC = () => {
  const [examId, setExamId] = useState<string>("");
  const [exams, setExams] = useState<Exam[]>([]);
  const [examsLoading, setExamsLoading] = useState(false);
  const [applicants, setApplicants] = useState<Applicant[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedApplicant, setSelectedApplicant] = useState<Applicant | null>(
    null
  );
  const [studentPhotos, setStudentPhotos] = useState<ExamPhoto[]>([]);
  const [photosLoading, setPhotosLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const PAGE_SIZE = 10;

  const fetchExams = useCallback(async () => {
    setExamsLoading(true);
    try {
      const result = await getExams(1, 50); // Get first 100 exams
      if (result.success !== false && result.exams) {
        setExams(result.exams);
      } else {
        toast.error("Failed to fetch exams");
        setExams([]);
      }
    } catch (error) {
      console.error("Error fetching exams:", error);
      toast.error("Failed to fetch exams");
      setExams([]);
    } finally {
      setExamsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchExams();
  }, [fetchExams]);

  const fetchApplicants = useCallback(
    async (page: number = 1) => {
      if (!examId) {
        toast.error("Please enter an exam ID");
        return;
      }

      setLoading(true);
      try {
        const result = await getExamApplicant(
          parseInt(examId),
          page,
          PAGE_SIZE
        );

        if (result.success === false) {
          toast.error(result.error || "Failed to fetch exam applicants");
          setApplicants([]);
          return;
        }

        setApplicants(result.data || []);
        setTotalItems(result.total || 0);
        setTotalPages(Math.ceil((result.total || 0) / PAGE_SIZE));
        setCurrentPage(page);

        toast.success(`Found ${result.data?.length || 0} exam applicants`);
      } catch (error) {
        console.error("Error fetching exam applicants:", error);
        toast.error("Failed to fetch exam applicants");
        setApplicants([]);
      } finally {
        setLoading(false);
      }
    },
    [examId]
  );

  const fetchStudentPhotos = async (applicant: Applicant) => {
    if (!examId) {
      toast.error("Please enter an exam ID");
      return;
    }

    setPhotosLoading(true);
    setSelectedApplicant(applicant);

    try {
      const result = await getStudentExamPhotos(applicant.id, parseInt(examId));
      if (result.success && result.data) {
        setStudentPhotos(Array.isArray(result.data) ? result.data : []);
        toast.success(
          `Found ${
            Array.isArray(result.data) ? result.data.length : 0
          } photos for ${applicant.firstName} ${applicant.lastName}`
        );
      } else {
        setStudentPhotos([]);
        toast.error(result.error || "No photos found for this student");
      }
    } catch (error) {
      console.error("Error fetching student photos:", error);
      toast.error("Failed to fetch student photos");
      setStudentPhotos([]);
    } finally {
      setPhotosLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchApplicants(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchApplicants(page);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getPhotoUrl = (photoUrl: string) => {
    const baseUrl =
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:4005/api/v1";
    const serverBaseUrl = baseUrl.replace("/api/v1", "");
    const fullUrl = `${serverBaseUrl}${photoUrl}`;
    console.log("Photo URL:", { photoUrl, baseUrl, serverBaseUrl, fullUrl });
    return fullUrl;
  };

  const downloadPhoto = (photo: ExamPhoto) => {
    const link = document.createElement("a");
    link.href = getPhotoUrl(photo.photoUrl);
    link.download = `exam_${examId}_student_${photo.studentId}_${photo.id}.jpg`;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const columns: ColumnDef<Applicant>[] = [
    {
      accessorKey: "firstName",
      header: "First Name",
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "contact",
      header: "Contact",
    },
    {
      accessorKey: "createdAt",
      header: "Applied Date",
      cell: ({ row }) => (
        <span className="text-sm text-gray-600">
          {new Date(row.original.createdAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => fetchStudentPhotos(row.original)}
          className="flex items-center gap-2"
        >
          <Camera className="w-4 h-4" />
          View Photos
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Search and Filter Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Student Photo Monitoring
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <Label htmlFor="examSelect">Select Exam</Label>
                <Select value={examId} onValueChange={setExamId}>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        examsLoading ? "Loading exams..." : "Select an exam"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {exams.map((exam) => (
                      <SelectItem key={exam.id} value={exam.id.toString()}>
                        <div className="flex">
                          <span className="font-medium">{exam.exam_name}</span>
                          <span className="text-sm ml-4 text-gray-500">
                            {new Date(exam.start_date).toLocaleDateString()}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={handleSearch} disabled={loading || !examId}>
                <Search className="w-4 h-4 mr-2" />
                {loading ? "Loading..." : "Load Exam Applicants"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exam Applicants Table */}
      {applicants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Exam Applicants ({totalItems} total)</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable
              columns={columns}
              data={applicants}
              isLoading={loading}
            />

            <Pagination
              page={currentPage}
              totalPages={totalPages}
              setPage={handlePageChange}
              entriesText={`${totalItems} entries`}
            />
          </CardContent>
        </Card>
      )}

      {/* Photo Modal */}
      {selectedApplicant && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl max-h-[90vh] overflow-auto w-full">
            <div className="p-4 border-b sticky top-0 bg-white">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-semibold text-lg">
                    {selectedApplicant.firstName} {selectedApplicant.lastName}
                  </h3>
                  <p className="text-sm text-gray-500">
                    Exam ID: {examId} | Email: {selectedApplicant.email}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => fetchStudentPhotos(selectedApplicant)}
                    disabled={photosLoading}
                  >
                    <RefreshCw
                      className={`w-4 h-4 mr-2 ${
                        photosLoading ? "animate-spin" : ""
                      }`}
                    />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedApplicant(null);
                      setStudentPhotos([]);
                    }}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="p-4">
              {photosLoading ? (
                <div className="flex justify-center items-center py-12">
                  <RefreshCw className="w-8 h-8 animate-spin text-primary" />
                </div>
              ) : studentPhotos.length > 0 ? (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Found {studentPhotos.length} photos for this student
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {studentPhotos.map((photo) => (
                      <div
                        key={photo.id}
                        className="border rounded-lg overflow-hidden"
                      >
                        <div className="aspect-video relative">
                          <Image
                            src={getPhotoUrl(photo.photoUrl)}
                            alt={`Photo captured at ${formatDate(
                              photo.capturedAt
                            )}`}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                          />
                        </div>
                        <div className="p-2">
                          <div className="flex items-center gap-1 text-xs text-gray-500 mb-2">
                            <Calendar className="w-3 h-3" />
                            {formatDate(photo.capturedAt)}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full"
                            onClick={() => downloadPhoto(photo)}
                          >
                            <Download className="w-3 h-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Camera className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">
                    No photos found for this student in exam {examId}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {applicants.length === 0 && !loading && examId && (
        <Card>
          <CardContent className="text-center py-8">
            <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">
              No exam applicants found for the selected exam.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default StudentPhotoMonitoring;
