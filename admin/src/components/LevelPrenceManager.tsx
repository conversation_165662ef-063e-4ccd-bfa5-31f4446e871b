"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  createLevelPrefrence,
  getLevelPrefrence,
  deleteLevelPrefrence,
} from "@/services/uwhizLevelPrefrenceApi";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Plus, Trash2Icon } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import ConfirmDialog from "@/app-components/ConfirmDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Pagination from "@/app-components/pagination";


const formSchema = z.object({
  level: z.string().min(1, "Level is required"),
});

interface LevelPreference {
  id: string;
  level: string;
  weightage: number;
  timePerQuestion: number;
}

interface FormValues {
  level: string;
}

interface LevelPreferenceManagerProps {
  examId: string;
}

export default function LevelPreferenceManager({
  examId,
}: LevelPreferenceManagerProps) {
  const [levelPreferences, setLevelPreferences] = useState<LevelPreference[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const totalItems = levelPreferences.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const paginatedData = levelPreferences.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const levels = ["EASY", "MEDIUM", "HARD"];

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: { level: "" },
  });

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await getLevelPrefrence(Number(examId));
      if (response.success) {
        setLevelPreferences(
          Array.isArray(response.data.data) ? response.data.data : []
        );
      } else {
        toast.error(response.error || "Failed to fetch level preferences");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch data");
    } finally {
      setIsLoading(false);
    }
  }, [examId]);

  useEffect(() => {
    if (examId) fetchData();
  }, [examId, fetchData]);

  const onSubmit = async (values: FormValues) => {
    try {
      const response = await createLevelPrefrence({
        examId: Number(examId),
        level: values.level,
        weightage: 0,
      });
      if (response.success) {
        toast.success("Level preference added successfully");
        setIsDialogOpen(false);
        form.reset();
        fetchData();
      } else {
        toast.error(response.error || "Failed to add level preference");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to add level preference");
    }
  };

  const handleDelete = (id: string) => setDeleteId(id);

  const confirmDelete = async () => {
    try {
      if (deleteId) {
        const response = await deleteLevelPrefrence(deleteId);
        if (response.success) {
          toast.success("Level preference deleted successfully");
          setDeleteId(null);
          fetchData();
        } else {
          toast.error(response.error || "Failed to delete level preference");
        }
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete level preference");
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
    form.reset();
  };

  const columns: ColumnDef<LevelPreference>[] = [
    { accessorKey: "level", header: "Level" },
    {
      accessorKey: "weightage",
      header: "Weightage (%)",
      cell: ({ row }) => <div>{row.original.weightage.toFixed(2)}%</div>,
    },
    { accessorKey: "timePerQuestion", header: "Time per Question (s)" },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-100"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2Icon className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Level Preferences</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Level Preference
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Level Preference</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Level</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a level" />
                          </SelectTrigger>
                          <SelectContent>
                            {levels.map((level) => (
                              <SelectItem key={level} value={level}>
                                {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Save</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <DataTable
        data={paginatedData}
        columns={columns}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${totalItems} entries`}
      />

      <ConfirmDialog
        open={!!deleteId}
        setOpen={(val) => !val && setDeleteId(null)}
        title="Are you sure?"
        description="This action cannot be undone. This will permanently delete the level preference."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        isLoading={isLoading}
      />
    </div>
  );
}
