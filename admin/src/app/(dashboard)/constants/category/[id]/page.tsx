'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Plus, Search, Trash2, Edit, ArrowLeft, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import Pagination from '@/app-components/pagination';
import {
  getCategoryById,
  getDetailsByCategory,
  createDetail,
  updateDetail,
  deleteDetail
} from '@/services/constantsApi';
import { ConstantCategory, ConstantDetail } from '@/lib/types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

export default function CategoryDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const categoryId = params.id as string;

  const [category, setCategory] = useState<ConstantCategory | null>(null);
  const [details, setDetails] = useState<ConstantDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState('');

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingDetail, setEditingDetail] = useState<ConstantDetail | null>(null);
  const [newDetailName, setNewDetailName] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingDetail, setDeletingDetail] = useState<ConstantDetail | null>(null);

  const fetchCategory = useCallback(async () => {
    try {
      const data = await getCategoryById(categoryId);
      setCategory(data);
    } catch {
      toast.error('Category not found');
    }
  }, [categoryId]);

  const fetchDetails = useCallback(async (page: number = currentPage, search?: string) => {
    try {
      setLoading(true);
      const result = await getDetailsByCategory(categoryId, page, pageSize, search);
      setDetails(result.data);
      setTotalPages(result.pagination.totalPages);
      setTotalRecords(result.pagination.total);
    } catch {
      toast.error('Failed to fetch details');
    } finally {
      setLoading(false);
    }
  }, [categoryId, currentPage, pageSize]);

  const createDetailHandler = async () => {
    if (!newDetailName.trim()) {
      toast.error('Detail name is required');
      return;
    }

    try {
      await createDetail({
        name: newDetailName,
        categoryId: categoryId
      });
      toast.success('Detail created successfully');
      setNewDetailName('');
      setIsAddDialogOpen(false);
      fetchDetails();
    } catch {
      toast.error('Failed to create detail');
    }
  };

  const updateDetailHandler = async () => {
    if (!editingDetail || !newDetailName.trim()) {
      toast.error('Detail name is required');
      return;
    }

    try {
      await updateDetail(editingDetail.id, { name: newDetailName });
      toast.success('Detail updated successfully');
      setNewDetailName('');
      setIsEditDialogOpen(false);
      setEditingDetail(null);
      fetchDetails();
    } catch {
      toast.error('Failed to update detail');
    }
  };



  const handleEdit = useCallback((detail: ConstantDetail) => {
    setEditingDetail(detail);
    setNewDetailName(detail.name);
    setIsEditDialogOpen(true);
  }, []);



  const handleDeleteClick = useCallback((detail: ConstantDetail) => {
    setDeletingDetail(detail);
    setIsDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!deletingDetail) return;

    try {
      await deleteDetail(deletingDetail.id);
      toast.success('Detail deleted successfully');
      setCurrentPage(1);
      fetchDetails(1);
    } catch {
      toast.error('Cannot delete detail as it contains sub-details or is being used');
    } finally {
      setIsDeleteDialogOpen(false);
      setDeletingDetail(null);
    }
  }, [deletingDetail, fetchDetails]);

  const applySearch = useCallback(() => {
    setAppliedSearchTerm(searchTerm);
    setCurrentPage(1);
    fetchDetails(1, searchTerm.trim() || undefined);
  }, [searchTerm, fetchDetails]);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setAppliedSearchTerm('');
    setCurrentPage(1);
    fetchDetails(1, undefined);
  }, [fetchDetails]);

  const handleSearchKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applySearch();
    }
  }, [applySearch]);



  const columns = useMemo<ColumnDef<ConstantDetail>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },
    {
      id: 'subDetails',
      header: 'Sub-Details',
      cell: ({ row }) => (
        <Badge variant="secondary">{row.original.subDetails.length}</Badge>
      ),
    },
    {
      id: 'values',
      header: 'Values',
      cell: ({ row }) => {
        const totalValues = row.original.subDetails.reduce((sum, subDetail) => sum + subDetail.values.length, 0);
        return <Badge variant="secondary">{totalValues}</Badge>;
      },
    },
    {
      id: 'actions',
      header: () => (
        <div className="text-right">Actions</div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/constants/category/${categoryId}/detail/${row.original.id}`)}
            title="Manage Sub-Details"
            className="h-8 w-8 p-0"
          >
            <FolderOpen className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
            title="Edit Detail"
            className="h-8 w-8 p-0"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(row.original)}
            title="Delete Detail"
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
  ], [handleEdit, handleDeleteClick, router, categoryId]);

  useEffect(() => {
    fetchCategory();
    fetchDetails(1);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchCategory]);

  useEffect(() => {
    if (appliedSearchTerm) {
      fetchDetails(currentPage, appliedSearchTerm);
    } else {
      fetchDetails(currentPage);
    }
  }, [currentPage, appliedSearchTerm, fetchDetails]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading details...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {category?.name} - Details
            </h1>
            <p className="text-gray-600 mt-1">Manage details for this category</p>
          </div>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Detail
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Detail</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="detailName">Detail Name</Label>
                <Input
                  id="detailName"
                  value={newDetailName}
                  onChange={(e) => setNewDetailName(e.target.value)}
                  placeholder="Enter detail name"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createDetailHandler}>Create</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>


      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search details..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleSearchKeyPress}
              className="pl-10 w-64"
            />
          </div>
          <Button
            onClick={applySearch}
            size="sm"
            disabled={!searchTerm.trim()}
            className="bg-black text-white hover:bg-gray-800"
          >
            Search
          </Button>
          {appliedSearchTerm && (
            <Button
              onClick={clearSearch}
              variant="ghost"
              size="sm"
            >
              Clear
            </Button>
          )}
        </div>

      </div>


      <Card>
        <CardHeader>
          <CardTitle>Details ({totalRecords})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            columns={columns}
            data={details}
            isLoading={loading}
          />
        </CardContent>
      </Card>

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          fetchDetails(page);
        }}
        entriesText={`${totalRecords} entries`}
      />


      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Detail</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editDetailName">Detail Name</Label>
              <Input
                id="editDetailName"
                value={newDetailName}
                onChange={(e) => setNewDetailName(e.target.value)}
                placeholder="Enter detail name"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={updateDetailHandler}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>


      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Are you sure you want to delete the detail <strong>&quot;{deletingDetail?.name}&quot;</strong>?</p>
            <p className="text-sm text-gray-600">This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDelete}
              >
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
