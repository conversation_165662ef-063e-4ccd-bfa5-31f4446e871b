'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Plus, Search, Trash2, Edit, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import Pagination from '@/app-components/pagination';
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory
} from '@/services/constantsApi';
import { ConstantCategory } from '@/lib/types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

export default function ConstantsPage() {
  const [categories, setCategories] = useState<ConstantCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState('');

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ConstantCategory | null>(null);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingCategory, setDeletingCategory] = useState<ConstantCategory | null>(null);

  const fetchCategories = useCallback(async (page: number = currentPage, search?: string) => {
    try {
      setLoading(true);
      const result = await getCategories(page, pageSize, search);
      setCategories(result.data);
      setTotalPages(result.pagination.totalPages);
      setTotalRecords(result.pagination.total);
    } catch {
      toast.error('Failed to fetch categories');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize]);

  const createCategoryHandler = async () => {
    if (!newCategoryName.trim()) {
      toast.error('Category name is required');
      return;
    }

    try {
      await createCategory({ name: newCategoryName });
      toast.success('Category created successfully');
      setNewCategoryName('');
      setIsAddDialogOpen(false);
      fetchCategories();
    } catch {
      toast.error('Failed to create category');
    }
  };

  const updateCategoryHandler = async () => {
    if (!editingCategory || !newCategoryName.trim()) {
      toast.error('Category name is required');
      return;
    } 

    try {
      await updateCategory(editingCategory.id, { name: newCategoryName });
      toast.success('Category updated successfully');
      setNewCategoryName('');
      setIsEditDialogOpen(false);
      setEditingCategory(null);
      fetchCategories();
    } catch {
      toast.error('Failed to update category');
    }
  };

  const handleDeleteClick = useCallback((category: ConstantCategory) => {
    setDeletingCategory(category);
    setIsDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!deletingCategory) return;

    try {
      await deleteCategory(deletingCategory.id);
      toast.success('Category deleted successfully');
      setCurrentPage(1);
      fetchCategories(1);
    } catch {
      toast.error('Cannot delete category as it contains details or is being used');
    } finally {
      setIsDeleteDialogOpen(false);
      setDeletingCategory(null);
    }
  }, [deletingCategory, fetchCategories]);



  const handleEdit = useCallback((category: ConstantCategory) => {
    setEditingCategory(category);
    setNewCategoryName(category.name);
    setIsEditDialogOpen(true);
  }, []);



  const applySearch = useCallback(() => {
    setAppliedSearchTerm(searchTerm);
    setCurrentPage(1);
    fetchCategories(1, searchTerm.trim() || undefined);
  }, [searchTerm, fetchCategories]);


  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setAppliedSearchTerm('');
    setCurrentPage(1);
    fetchCategories(1, undefined);
  }, [fetchCategories]);

  const handleSearchKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applySearch();
    }
  }, [applySearch]);



  const columns = useMemo<ColumnDef<ConstantCategory>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },
    {
      id: 'details',
      header: 'Details',
      cell: ({ row }) => (
        <Badge variant="secondary">{row.original.details.length}</Badge>
      ),
    },
    {
      id: 'subDetails',
      header: 'Sub-Details',
      cell: ({ row }) => {
        const totalSubDetails = row.original.details.reduce((sum, detail) => sum + detail.subDetails.length, 0);
        return <Badge variant="secondary">{totalSubDetails}</Badge>;
      },
    },
    {
      id: 'values',
      header: 'Values',
      cell: ({ row }) => {
        const totalValues = row.original.details.reduce((sum, detail) =>
          sum + detail.subDetails.reduce((subSum, subDetail) => subSum + subDetail.values.length, 0), 0
        );
        return <Badge variant="secondary">{totalValues}</Badge>;
      },
    },
    {
      id: 'actions',
      header: () => (
        <div className="text-right">Actions</div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.location.href = `/constants/category/${row.original.id}`}
            title="Manage Details"
            className="h-8 w-8 p-0"
          >
            <Settings className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
            title="Edit Category"
            className="h-8 w-8 p-0"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(row.original)}
            title="Delete Category"
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
  ], [handleEdit, handleDeleteClick]);

  useEffect(() => {
    fetchCategories(1);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (appliedSearchTerm) {
      fetchCategories(currentPage, appliedSearchTerm);
    } else {
      fetchCategories(currentPage);
    }
  }, [currentPage, appliedSearchTerm, fetchCategories]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading categories...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Constants Management</h1>
          <p className="text-gray-600 mt-1">Manage your application constants and categories</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Category
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Category</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="categoryName">Category Name</Label>
                <Input
                  id="categoryName"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  placeholder="Enter category name"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createCategoryHandler}>Create</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleSearchKeyPress}
              className="pl-10 w-64"
            />
          </div>
          <Button
            onClick={applySearch}
            size="sm"
            disabled={!searchTerm.trim()}
            className="bg-black text-white hover:bg-gray-800"
          >
            Search
          </Button>
          {appliedSearchTerm && (
            <Button
              onClick={clearSearch}
              variant="ghost"
              size="sm"
            >
              Clear
            </Button>
          )}
        </div>

      </div>

      <Card>
        <CardHeader>
          <CardTitle>Categories ({totalRecords})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            columns={columns}
            data={categories}
            isLoading={loading}
          />
        </CardContent>
      </Card>

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          fetchCategories(page);
        }}
        entriesText={`${totalRecords} entries`}
      />

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editCategoryName">Category Name</Label>
              <Input
                id="editCategoryName"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                placeholder="Enter category name"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={updateCategoryHandler}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Are you sure you want to delete the category <strong>&quot;{deletingCategory?.name}&quot;</strong>?</p>
            <p className="text-sm text-gray-600">This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDelete}
              >
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}