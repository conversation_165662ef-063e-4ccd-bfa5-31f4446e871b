'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { ArrowLeft, Loader2, Camera, Upload, X, FileText } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { updateStudentByAdmin } from '@/services/studentApi';
import Image from 'next/image';
import { axiosInstance } from '@/lib/axios';

const profileFormSchema = z.object({
    firstName: z.string().min(2, 'First name must be at least 2 characters'),
    lastName: z.string().min(2, 'Last name must be at least 2 characters'),
    contact: z.string()
        .min(10, 'Contact number must be at least 10 digits')
        .regex(/^\d+$/, 'Contact number must contain only digits'),
    medium: z.string().min(1, 'Medium is required'),
    classroom: z.string().min(1, 'Class is required'),
    birthday: z.date({
        required_error: 'Birthday is required',
    }),
    school: z.string().min(2, 'School name must be at least 2 characters'),
    address: z.string().min(5, 'Address must be at least 5 characters'),
    document: z.any().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;
type DocumentType = File | { name: string; size: number; url: string; type: string };

const StudentEditPage = () => {
    const params = useParams();
    const router = useRouter();
    const studentId = params.id as string;

    const [isLoading, setIsLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const [photo, setPhoto] = useState<string | null>(null);
    const [uploadedPhoto, setUploadedPhoto] = useState<File | null>(null);
    const [uploadedDocument, setUploadedDocument] = useState<DocumentType | null>(null);
    const [classroomOptions, setClassroomOptions] = useState<Array<{id: number, value: string}>>([]);
    const [mediumOptions] = useState([
        { id: 1, value: 'english' },
        { id: 2, value: 'gujarati' }
    ]);

    const form = useForm<ProfileFormValues>({
        resolver: zodResolver(profileFormSchema),
        defaultValues: {
            firstName: '',
            lastName: '',
            contact: '',
            medium: '',
            classroom: '',
            birthday: undefined,
            school: '',
            address: '',
        },
        mode: 'onSubmit',
    });

    const fetchStudentData = useCallback(async () => {
        try {
            setIsLoading(true);

            const response = await axiosInstance.get(`/student-profile/admin/${studentId}/all-data`);
            const allData = response.data.data;
            const profileData = allData.profile;

            // Set classroom options - use provided options or fetch constants
            const classroomOpts = allData.classroomOptions || [];
            if (classroomOpts.length > 0) {
                setClassroomOptions(classroomOpts);
            } else {
                const constantsResponse = await axiosInstance.get('/constant/classroom');
                const formattedOptions = constantsResponse.data.details?.map((detail: any, index: number) => ({
                    id: detail.id || index + 1,
                    value: detail.value
                })) || [];
                setClassroomOptions(formattedOptions);
            }

            // Set form values
            const formValues = {
                firstName: profileData.student?.firstName || '',
                lastName: profileData.student?.lastName || '',
                contact: profileData.student?.contact || '',
                medium: profileData.medium || '',
                classroom: profileData.classroom || '',
                school: profileData.school || '',
                address: profileData.address || '',
                birthday: profileData.birthday ? new Date(profileData.birthday) : undefined
            };

            Object.entries(formValues).forEach(([key, value]) => {
                form.setValue(key as any, value);
            });

            // Set photo
            if (profileData.photo) {
                setPhoto(profileData.photo);
            }

            // Set document
            if (profileData.documentUrl) {
                const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';
                const documentUrl = profileData.documentUrl.startsWith('http')
                    ? profileData.documentUrl
                    : `${baseUrl}${profileData.documentUrl}`;

                setUploadedDocument({
                    name: documentUrl.split('/').pop() || 'Uploaded Document',
                    size: 0,
                    url: documentUrl,
                    type: 'application/octet-stream'
                });
            }

        } catch (error) {
            console.error('Failed to fetch student data:', error);
            toast.error('Failed to load student data');
        } finally {
            setIsLoading(false);
        }
    }, [studentId, form]);

    useEffect(() => {
        fetchStudentData();
    }, [fetchStudentData]);

    const formatFileSize = (bytes: number) => bytes < 1048576 ? (bytes / 1024).toFixed(1) + ' KB' : (bytes / 1048576).toFixed(1) + ' MB';

    const handlePhotoUpload = (file: File) => {
        if (file.size > 5 * 1024 * 1024) {
            toast.error("Photo size exceeds 5MB limit");
            return;
        }
        setUploadedPhoto(file);
        const reader = new FileReader();
        reader.onload = (e) => setPhoto(e.target?.result as string);
        reader.readAsDataURL(file);
    };



    const onSubmit = async (data: ProfileFormValues) => {
        try {
            setIsSaving(true);

            const jsonData: any = {
                firstName: data.firstName,
                lastName: data.lastName,
                contact: data.contact,
                medium: data.medium,
                classroom: data.classroom,
                birthday: data.birthday?.toISOString() || '',
                school: data.school,
                address: data.address
            };

            // Handle photo upload
            if (uploadedPhoto instanceof File && uploadedPhoto.size > 0) {
                jsonData.photo = await fileToBase64(uploadedPhoto);
                jsonData.photoMimeType = uploadedPhoto.type;
            }

            // Handle document upload
            if (uploadedDocument instanceof File && uploadedDocument.size > 0) {
                jsonData.document = await fileToBase64(uploadedDocument);
                jsonData.documentMimeType = uploadedDocument.type;
                jsonData.documentName = uploadedDocument.name;
            }

            await updateStudentByAdmin(studentId, jsonData);
            toast.success('Student updated successfully!');
            router.push('/student-details');
        } catch (error: any) {
            console.error('Failed to update student:', error);
            toast.error(error.message || 'Failed to update student');
        } finally {
            setIsSaving(false);
        }
    };

    const fileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const result = reader.result as string;
                const base64 = result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = error => reject(error);
        });
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
        );
    }

    return (
        <div className="container mx-auto py-6 px-4 max-w-6xl">
            <div className="flex items-center gap-4 mb-6">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => router.back()}
                    className="hover:bg-gray-100"
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-2xl font-bold">Edit Student</h1>
            </div>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-medium">Personal Information</CardTitle>
                            <CardDescription>Update student personal details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormField
                                    control={form.control}
                                    name="firstName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">First Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter first name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="lastName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Last Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter last name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="contact"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Contact Number</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter contact number"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-medium">Profile Information</CardTitle>
                            <CardDescription>Update student profile details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <FormField
                                    control={form.control}
                                    name="medium"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Medium</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg">
                                                        <SelectValue placeholder="Select medium" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {mediumOptions.map((option) => (
                                                        <SelectItem key={option.id} value={option.value}>
                                                            {option.value.charAt(0).toUpperCase() + option.value.slice(1)}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="classroom"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Class</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg">
                                                        <SelectValue placeholder="Select class" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {classroomOptions.length > 0 ? (
                                                        classroomOptions.map((option) => (
                                                            <SelectItem key={option.id} value={option.value}>
                                                                {option.value}
                                                            </SelectItem>
                                                        ))
                                                    ) : (
                                                        <div className="p-2 text-center text-gray-500">
                                                            No classroom options available
                                                        </div>
                                                    )}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="birthday"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">Birthday</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="date"
                                                    value={field.value ? (() => {
                                                        const date = new Date(field.value);
                                                        const year = date.getFullYear();
                                                        const month = String(date.getMonth() + 1).padStart(2, '0');
                                                        const day = String(date.getDate()).padStart(2, '0');
                                                        return `${year}-${month}-${day}`;
                                                    })() : ''}
                                                    onChange={(e) => {
                                                        if (e.target.value) {
                                                            const date = new Date(e.target.value + 'T00:00:00');
                                                            field.onChange(date);
                                                        } else {
                                                            field.onChange(undefined);
                                                        }
                                                    }}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="school"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel className="text-black font-medium">School Name</FormLabel>
                                            <FormControl>
                                                <Input
                                                    {...field}
                                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                    placeholder="Enter school name"
                                                />
                                            </FormControl>
                                            <FormMessage className="text-red-500" />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="address"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="text-black font-medium">Address</FormLabel>
                                        <FormControl>
                                            <Input
                                                {...field}
                                                className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                                placeholder="Enter address"
                                            />
                                        </FormControl>
                                        <FormMessage className="text-red-500" />
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>

                    {/* Photo Upload */}
                    <Card className="shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-medium">Profile Photo</CardTitle>
                            <CardDescription>Upload or update student profile photo</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {/* Photo Display */}
                                {(uploadedPhoto || photo) ? (
                                    <div className="flex justify-center">
                                        <div className="border rounded-lg shadow-md bg-gray-50 p-4 max-w-full">
                                            <div className="flex justify-center">
                                                <Image
                                                    src={uploadedPhoto ? URL.createObjectURL(uploadedPhoto) :
                                                         photo?.startsWith('http') ? photo :
                                                         `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${photo}?t=${new Date().getTime()}`}
                                                    alt="Student Photo"
                                                    className="max-w-full max-h-96 object-contain rounded-lg"
                                                    height={1000}
                                                    width={1000}
                                                    style={{ height: 'auto', width: 'auto' }}
                                                    onError={(e) => e.currentTarget.style.display = 'none'}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex items-center justify-center h-36 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                                        <div className="text-center">
                                            <Camera className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                                            <p className="text-sm text-gray-500">No photo available</p>
                                        </div>
                                    </div>
                                )}

                                {/* Upload Controls */}
                                <div className="flex justify-center space-x-2">
                                    <label>
                                        <Button type="button" variant="outline" size="sm" asChild>
                                            <span>{(uploadedPhoto || photo) ? 'Change Photo' : 'Upload Photo'}</span>
                                        </Button>
                                        <Input
                                            type="file"
                                            accept=".jpg,.jpeg,.png"
                                            className="hidden"
                                            onChange={(e) => {
                                                const file = e.target.files?.[0];
                                                if (file) handlePhotoUpload(file);
                                            }}
                                        />
                                    </label>
                                    {(uploadedPhoto || photo) && (
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                setUploadedPhoto(null);
                                                setPhoto(null);
                                            }}
                                        >
                                            Remove Photo
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <FormField
                        control={form.control}
                        name="document"
                        render={({ field }) => (
                            <Card className="shadow-sm">
                                <CardHeader>
                                    <CardTitle className="text-lg font-medium">Identity Document</CardTitle>
                                    <CardDescription>
                                        Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <FormItem>
                                        {!uploadedDocument ? (
                                            <FormControl>
                                                <div className="flex items-center justify-center w-full">
                                                    <label className="flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                            <Upload className="w-10 h-10 mb-3 text-black" />
                                                            <p className="mb-2 text-sm text-gray-700"><span className="font-semibold">Click to upload</span> or drag and drop</p>
                                                            <p className="text-xs text-gray-500">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>
                                                        </div>
                                                        <Input
                                                            id="document"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png"
                                                            className="hidden"
                                                            onChange={(e) => {
                                                                const file = e.target.files?.[0];
                                                                if (file) {
                                                                    if (file.size > 5 * 1024 * 1024) {
                                                                        toast.error("File size exceeds 5MB limit");
                                                                        return;
                                                                    }
                                                                    setUploadedDocument(file);
                                                                    field.onChange(file);
                                                                }
                                                            }}
                                                        />
                                                    </label>
                                                </div>
                                            </FormControl>
                                        ) : (
                                            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-[#fff8f3] rounded-full">
                                                            <FileText className="h-5 w-5 text-black" />
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium text-gray-700">{uploadedDocument.name}</p>
                                                            <p className="text-xs text-gray-500">
                                                                {uploadedDocument instanceof File
                                                                    ? formatFileSize(uploadedDocument.size)
                                                                    : 'Previously uploaded document'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="flex space-x-2">
                                                        {uploadedDocument && 'url' in uploadedDocument && (
                                                            <Button
                                                                type="button"
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => {
                                                                    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005';
                                                                    const url = uploadedDocument.url.startsWith('http')
                                                                        ? uploadedDocument.url
                                                                        : `${baseUrl}${uploadedDocument.url.startsWith('/') ? '' : '/'}${uploadedDocument.url}`;
                                                                    window.open(url, '_blank');
                                                                }}
                                                                className="h-8 px-3 border-gray-200"
                                                            >
                                                                View
                                                            </Button>
                                                        )}

                                                        {/* Change document button */}
                                                        <div>
                                                            <Input
                                                                type="file"
                                                                accept=".pdf,.jpg,.jpeg,.png"
                                                                onChange={(e) => {
                                                                    const file = e.target.files?.[0];
                                                                    if (file) {
                                                                        if (file.size > 5 * 1024 * 1024) {
                                                                            toast.error("File size exceeds 5MB limit");
                                                                            return;
                                                                        }
                                                                        setUploadedDocument(file);
                                                                        field.onChange(file);
                                                                    }
                                                                }}
                                                                className="hidden"
                                                                id="document-change"
                                                            />
                                                            <label
                                                                htmlFor="document-change"
                                                                className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium border border-gray-200 bg-white hover:bg-gray-50 h-8 px-3"
                                                            >
                                                                Change
                                                            </label>
                                                        </div>

                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => setUploadedDocument(null)}
                                                            className="h-8 w-8 p-0 border-gray-200"
                                                        >
                                                            <X className="h-4 w-4 text-gray-500" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        <FormDescription className="text-xs text-gray-500 mt-2">
                                            This document will serve to verify your identity and date of birth, with your face clearly visible in the document.                          </FormDescription>
                                        <FormMessage className="text-red-500" />
                                    </FormItem>
                                </CardContent>
                            </Card>
                        )}
                    />

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.back()}
                            disabled={isSaving}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={isSaving}>
                            {isSaving ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Updating...
                                </>
                            ) : (
                                'Update Student'
                            )}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
};

export default StudentEditPage;