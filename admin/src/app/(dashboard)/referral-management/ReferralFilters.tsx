'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter, RotateCcw } from 'lucide-react';

export interface FilterValues {
  firstName: string;
  lastName: string;
  email: string;
  userType: string;
  paymentStatus: string;
  startDate: string;
  endDate: string;
}

interface ReferralFiltersProps {
  onFilterChange: (filters: FilterValues) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

export default function ReferralFilters({ onFilterChange, onClearFilters, isLoading = false }: ReferralFiltersProps) {
  const [filters, setFilters] = useState<FilterValues>({
    firstName: '',
    lastName: '',
    email: '',
    userType: '',
    paymentStatus: '',
    startDate: '',
    endDate: '',
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterChange = (key: keyof FilterValues, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  const applyFilters = () => {
    onFilterChange(filters);
  };

  const clearAllFilters = () => {
    const emptyFilters: FilterValues = {
      firstName: '',
      lastName: '',
      email: '',
      userType: '',
      paymentStatus: '',
      startDate: '',
      endDate: '',
    };
    setFilters(emptyFilters);
    onClearFilters();
  };

  const hasActiveFilters = () => {
    return filters.firstName || filters.lastName || filters.email || filters.userType || filters.paymentStatus || filters.startDate || filters.endDate;
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-orange-500" />
            Filters
            {hasActiveFilters() && (
              <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                Active
              </span>
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-600"
          >
            {isExpanded ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* Name and Email Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                placeholder="Search by first name..."
                value={filters.firstName}
                onChange={(e) => handleFilterChange('firstName', e.target.value)}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                placeholder="Search by last name..."
                value={filters.lastName}
                onChange={(e) => handleFilterChange('lastName', e.target.value)}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                placeholder="Search by email..."
                value={filters.email}
                onChange={(e) => handleFilterChange('email', e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Type, Payment Status and Date Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userType">User Type</Label>
              <Select value={filters.userType || "ALL"} onValueChange={(value) => handleFilterChange('userType', value === "ALL" ? "" : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select user type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="ADMIN">Admin/Staff</SelectItem>
                  <SelectItem value="CLASS">Class</SelectItem>
                  <SelectItem value="STUDENT">Student</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentStatus">Earnings Filter</Label>
              <Select value={filters.paymentStatus || "ALL"} onValueChange={(value) => handleFilterChange('paymentStatus', value === "ALL" ? "" : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by earnings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Referrals</SelectItem>
                  <SelectItem value="PAID">Has Paid Earnings</SelectItem>
                  <SelectItem value="UNPAID">Has Pending Earnings</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3 pt-4 border-t">
            <Button
              onClick={applyFilters}
              className="bg-orange-500 hover:bg-orange-600"
              disabled={isLoading}
            >
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>

            {hasActiveFilters() && (
              <Button
                variant="outline"
                onClick={clearAllFilters}
                disabled={isLoading}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear All
              </Button>
            )}

            <div className="text-sm text-gray-500 ml-auto">
              {hasActiveFilters() ? 'Filters applied' : 'No filters applied'}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
