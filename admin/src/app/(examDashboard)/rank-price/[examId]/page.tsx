"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  createPriceRank,
  getPriceRanksByExamId,
  updatePriceRank,
  deletePriceRank,
} from "@/services/uwhizPriceRankApi";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Plus, Edit, Trash2 } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
// import { DataTable } from "@/components/PriceRankTable";
import { DataTable } from "@/app-components/dataTable";
import ConfirmDialog from "@/app-components/ConfirmDialog";
import Pagination from "@/app-components/pagination";

// Form schema
const formSchema = z.object({
  rank: z.coerce.number().min(1, "Rank must be at least 1"),
  price: z.coerce.number().min(0, "Price must be non-negative"),
});

// Type definitions
interface PriceRank {
  id: string;
  rank: number;
  price: number;
  exam: {
    exam_name: string;
  };
}

interface FormValues {
  rank: number;
  price: number;
}

interface FormValues {
  rank: number;
  price: number;
}

export default function PriceRankManager() {
  const { examId } = useParams();
  const [priceRanks, setPriceRanks] = useState<PriceRank[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const totalItems = priceRanks.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      rank: 0,
      price: 0,
    },
  });

  // Fetch price ranks
  const fetchPriceRanks = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getPriceRanksByExamId(Number(examId));
      setPriceRanks(Array.isArray(data) ? data : []);
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch price ranks");
    } finally {
      setIsLoading(false);
    }
  }, [examId, setIsLoading, setPriceRanks]);

  useEffect(() => {
    if (examId) {
      fetchPriceRanks();
    }
  }, [examId, fetchPriceRanks]);

  // Form submission
  const onSubmit = async (values: FormValues) => {
    try {
      if (isEditing && editingId) {
        await updatePriceRank(editingId, {
          rank: values.rank,
          price: values.price,
        });
        toast.success("Price rank updated successfully");
      } else {
        await createPriceRank({
          examId: Number(examId),
          rank: values.rank,
          price: values.price,
        });
        toast.success("Price rank created successfully");
      }
      setIsDialogOpen(false);
      setIsEditing(false);
      setEditingId(null);
      form.reset();
      fetchPriceRanks();
    } catch (error: any) {
      toast.error(error.message || "Failed to process price rank");
    }
  };

  // Edit handler
  const handleEdit = (priceRank: PriceRank) => {
    setIsEditing(true);
    setEditingId(priceRank.id);
    form.setValue("rank", priceRank.rank);
    form.setValue("price", priceRank.price);
    setIsDialogOpen(true);
  };

  // Delete handler
  const handleDelete = (id: string) => {
    setDeleteId(id);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    try {
      if (deleteId) {
        await deletePriceRank(deleteId);
        toast.success("Price rank deleted successfully");
        setDeleteId(null);
        fetchPriceRanks();
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete price rank");
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  // Cancel edit/create
  const handleCancel = () => {
    setIsDialogOpen(false);
    setIsEditing(false);
    setEditingId(null);
    form.reset();
  };

  // Table columns
  const columns: ColumnDef<PriceRank>[] = [
    {
      accessorKey: "rank",
      header: "Rank",
    },
    {
      accessorKey: "price",
      header: "Price",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-red-500 hover:text-red-700 hover:bg-red-100"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">
          Price Rank - {priceRanks[0]?.exam.exam_name || "Exam"}
        </h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Rank
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {isEditing ? "Edit Price Rank" : "Create Price Rank"}
              </DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="rank"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rank</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          placeholder="Enter rank"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          placeholder="Enter price"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    {isEditing ? "Update" : "Create"}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <DataTable
          columns={columns}
          data={priceRanks.slice(
            (currentPage - 1) * pageSize,
            currentPage * pageSize
          )}
          isLoading={isLoading}
        />
      )}

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${priceRanks.length} entries`}
      />

      <ConfirmDialog
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
        title="Are you sure?"
        description="This action cannot be undone. This will permanently delete the price rank."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        isLoading={isLoading}
      />
    </div>
  );
}
