"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { ColumnDef } from "@tanstack/react-table";
import axiosInstance from "@/lib/axios";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/app-components/dataTable";
import Pagination from "@/app-components/pagination";

type Ranking = {
  rank: number;
  firstName: string;
  lastName: string;
  email: string;
  score: number;
  attempts: number;
  totalQuestions: number;
};

export default function ExamRankingPage() {
  const { examId } = useParams();
  const [data, setData] = useState<Ranking[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEntries, setTotalEntries] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const limit = 10;

  const fetchRankings = async () => {
    if (!examId) return;
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `/uwhizResult/rankings/${examId}`,
        {
          headers: {
            "Server-Select": "uwhizServer",
          },
          params: { page, limit },
        }
      );

      if (response.data.success) {
        setData(response.data.data || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
        setTotalEntries(response.data.pagination?.totalItems || 0);
        setError(null);
      } else {
        setError("Failed to fetch rankings");
      }
    } catch (err: any) {
      setError("Error fetching rankings: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRankings();
  }, [examId, page]);

  const columns: ColumnDef<Ranking>[] = [
    {
      accessorKey: "rank",
      header: "Rank",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("rank")}</div>
      ),
    },
    {
      accessorKey: "firstName",
      header: "First Name",
      cell: ({ row }) => row.getValue("firstName") || "N/A",
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
      cell: ({ row }) => row.getValue("lastName") || "N/A",
    },
    {
      accessorKey: "email",
      header: "Student Email",
      cell: ({ row }) => row.getValue("email") || "N/A",
    },
    {
      accessorKey: "score",
      header: "Correct Answers",
    },
    {
      accessorKey: "attempts",
      header: "Attempts",
    },
    {
      accessorKey: "totalQuestions",
      header: "Total Questions",
    },
    {
      header: "Accuracy",
      cell: ({ row }) => {
        const { score, totalQuestions } = row.original;
        const accuracy = totalQuestions
          ? ((score / totalQuestions) * 100).toFixed(1)
          : "0.0";
        return `${accuracy}%`;
      },
    },
  ];

  if (!examId) return <div className="p-6">Invalid exam ID</div>;

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Exam Rankings</h1>

      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      ) : error ? (
        <div className="text-red-600">{error}</div>
      ) : (
        <>
          <DataTable
            columns={columns}
            data={data}
            isLoading={loading}
          />

          <Pagination
            page={page}
            totalPages={totalPages}
            setPage={setPage}
            entriesText={`${totalEntries} entries`}
          />
        </>
      )}
    </div>
  );
}
