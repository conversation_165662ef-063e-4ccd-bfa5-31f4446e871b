"use client";

import { useState, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Trash2, Download, Upload, Edit } from "lucide-react";
import { toast } from "sonner";
import {
  getQuestionBank,
  createQuestionBank,
  updateQuestionBank,
  deleteQuestionBank,
  getSubject,
  getClassroom,
  downloadQuestionBankExcel,
  uploadQuestionBankExcel,
  deleteManyQuestions,
} from "@/services/questionBankApi";
import { QuestionBank } from "@/lib/types";
import { Checkbox } from "@/components/ui/checkbox";
import ConfirmDialog from "@/app-components/ConfirmDialog";
import Pagination from "@/app-components/pagination";
import { DataTable } from "@/app-components/dataTable";
import dynamic from "next/dynamic";
import "react-quill-new/dist/quill.snow.css";
const ReactQuill = dynamic(() => import("react-quill-new"), {
  ssr: false,
});
import "react-quill-new/dist/quill.snow.css";

const questionBankSchema = z.object({
  question: z
    .string()
    .min(1, { message: "Question is required" })
    .max(500, { message: "Question cannot exceed 500 characters" }),
  optionOne: z
    .string()
    .min(1, { message: "Option 1 is required" })
    .max(100, { message: "Option 1 cannot exceed 100 characters" }),
  optionTwo: z
    .string()
    .min(1, { message: "Option 2 is required" })
    .max(100, { message: "Option 2 cannot exceed 100 characters" }),
  optionThree: z
    .string()
    .min(1, { message: "Option 3 is required" })
    .max(100, { message: "Option 3 cannot exceed 100 characters" }),
  optionFour: z
    .string()
    .min(1, { message: "Option 4 is required" })
    .max(100, { message: "Option 4 cannot exceed 100 characters" }),
  correctAnswer: z.enum(
    ["optionOne", "optionTwo", "optionThree", "optionFour"],
    {
      errorMap: () => ({ message: "Please select a correct answer" }),
    }
  ),
  medium: z.enum(["ENGLISH", "GUJARATI"], {
    errorMap: () => ({ message: "Please select a medium" }),
  }),
  level: z.enum(["EASY", "MEDIUM", "HARD"], {
    errorMap: () => ({ message: "Please select a level" }),
  }),
  standard: z.string().min(1, { message: "Please select a standard" }),
  subject: z.string().min(1, { message: "Please select a subject" }),
  chapter: z
    .enum(["Polynomial", "Statics", "Probability"], {
      errorMap: () => ({ message: "Please select a chapter" }),
    })
    .optional(),
});

type QuestionBankFormInput = z.infer<typeof questionBankSchema>;

const defaultFormValues: QuestionBankFormInput = {
  question: "",
  optionOne: "",
  optionTwo: "",
  optionThree: "",
  optionFour: "",
  correctAnswer: "optionOne",
  medium: "ENGLISH",
  level: "EASY",
  standard: "",
  subject: "",
  chapter: undefined,
};

export default function QuestionBankPage() {
  const [questions, setQuestions] = useState<QuestionBank[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuestionBank | null>(
    null
  );
  const [subjects, setSubjects] = useState<string[]>([]);
  const [classrooms, setClassrooms] = useState<string[]>([]);
  const [isFetchingConstants, setIsFetchingConstants] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [limit, setLimit] = useState(10);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [importDialog, setImportDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [selectedMedium, setSelectedMedium] = useState<
    "ENGLISH" | "GUJARATI" | "ALL"
  >("ENGLISH");
  const [selectedLevel, setSelectedLevel] = useState<
    "EASY" | "MEDIUM" | "HARD" | "ALL"
  >("EASY");
  const [selectedStandard, setSelectedStandard] = useState<string>("");
  const [selectedSubject, setSelectedSubject] = useState<string>();
  const [dialogMedium, setDialogMedium] = useState<"ENGLISH" | "GUJARATI">(
    "ENGLISH"
  );
  const [dialogLevel, setDialogLevel] = useState<"EASY" | "MEDIUM" | "HARD">(
    "EASY"
  );
  const [dialogStandard, setDialogStandard] = useState<string>("");
  const [dialogSubject, setDialogSubject] = useState<string>("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [deleteQuestionId, setDeleteQuestionId] = useState<string | null>(null);
  const [appliedFilters, setAppliedFilters] = useState({});

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
    setValue,
  } = useForm<QuestionBankFormInput>({
    resolver: zodResolver(questionBankSchema),
    defaultValues: defaultFormValues,
  });

  const fetchQuestions = async (page: number = 1, filters = {}) => {
    setIsLoading(true);
    const response = await getQuestionBank(page, limit, filters);
    if (response.success && response.data) {
      setQuestions(response.data.data);
      setTotalPages(response.data.pagination.totalPages);
      setTotalQuestions(response.data.pagination.totalQuestions);
      setCurrentPage(page);
    } else {
      setQuestions([]);
      setTotalPages(1);
      setTotalQuestions(0);
      toast.error(response.error || "Failed to fetch questions");
    }
    setIsLoading(false);
  };

  const fetchConstants = async () => {
    setIsFetchingConstants(true);
    setFetchError(null);
    try {
      const subjectResponse = await getSubject();
      if (subjectResponse.success && subjectResponse.data) {
        const subjectValues = subjectResponse.data.details
          .map((detail: any) => detail.value)
          .filter((value: string) => value && value.trim() !== "");
        setSubjects(subjectValues);
        if (subjectValues.length > 0) {
          setSelectedSubject(subjectValues[0]);
          setDialogSubject(subjectValues[0]);
          setValue("subject", subjectValues[0]);
        } else {
          setSelectedSubject("");
          setDialogSubject("");
          setValue("subject", "");
        }
      } else {
        setFetchError(subjectResponse.error || "Failed to fetch subjects");
        setSelectedSubject("");
        setDialogSubject("");
        setValue("subject", "");
      }

      const classroomResponse = await getClassroom();
      if (classroomResponse.success && classroomResponse.data) {
        const classroomValues = classroomResponse.data.details
          .map((detail: any) => detail.value)
          .filter((value: string) => value && value.trim() !== "");
        setClassrooms(classroomValues);
        if (classroomValues.length > 0) {
          setSelectedStandard(classroomValues[0]);
          setDialogStandard(classroomValues[0]);
          setValue("standard", classroomValues[0]);
        } else {
          setSelectedStandard("");
          setDialogStandard("");
          setValue("standard", "");
        }
      } else {
        setFetchError(
          (fetchError ? fetchError + "; " : "") +
            (classroomResponse.error || "Failed to fetch classrooms")
        );
        setSelectedStandard("");
        setDialogStandard("");
        setValue("standard", "");
      }

      setDialogMedium("ENGLISH");
      setDialogLevel("EASY");
      setValue("medium", "ENGLISH");
      setValue("level", "EASY");
    } catch (error) {
      setFetchError("Error fetching constants");
      console.error("Error fetching constants:", error);
      setSelectedStandard("");
      setSelectedSubject("");
      setDialogStandard("");
      setDialogSubject("");
      setValue("standard", "");
      setValue("subject", "");
      setValue("medium", "ENGLISH");
      setValue("level", "EASY");
    } finally {
      setIsFetchingConstants(false);
    }
  };

  useEffect(() => {
    fetchQuestions(1, {});
    fetchConstants();
  }, [limit]);

  const handleSearch = () => {
    setCurrentPage(1);
    const filters = {
      medium: selectedMedium === "ALL" ? undefined : selectedMedium,
      standard: selectedStandard || undefined,
      level: selectedLevel === "ALL" ? undefined : selectedLevel,
      subject: selectedSubject || undefined,
    };
    setAppliedFilters(filters);
    fetchQuestions(1, filters);
  };

  const handleReset = () => {
    setSelectedMedium("ALL");
    setSelectedLevel("ALL");
    setSelectedStandard(classrooms[0] || "");
    setSelectedSubject(subjects[0] || "");
    setCurrentPage(1);
    setAppliedFilters({});
    fetchQuestions(1, {});
  };

  const handleDownloadExcel = async () => {
    setIsDownloading(true);
    try {
      await downloadQuestionBankExcel(appliedFilters);
      toast.success("Excel file downloaded successfully");
    } catch (error) {
      toast.error("Failed to download Excel file");
      console.error("Download error:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleCheckboxChange = (id: string, checked: boolean) => {
    setSelectedQuestions((prev) =>
      checked ? [...prev, id] : prev.filter((questionId) => questionId !== id)
    );
  };

  const handleBulkDelete = async () => {
    if (selectedQuestions.length === 0) {
      toast.error("Please select at least one question to delete");
      return;
    }
    setIsBulkDeleteDialogOpen(true);
  };

  const handleBulkDeleteConfirm = async () => {
    const response = await deleteManyQuestions(selectedQuestions);
    if (response.success) {
      fetchQuestions(currentPage, appliedFilters);
      setSelectedQuestions([]);
      toast.success(
        response.data.data.message || "Selected questions deleted successfully!"
      );
    } else {
      toast.error(response.error || "Failed to delete selected questions");
    }
  };

  const onSubmit = async (data: QuestionBankFormInput) => {
    setIsSubmitting(true);
    setApiError(null);

    let response;
    if (editingQuestion) {
      const { chapter, ...rest } = data;
      const updateData = chapter ? { ...rest, chapter } : rest;
      response = await updateQuestionBank(
        editingQuestion.id.toString(),
        updateData as any
      );
    } else {
      const { chapter, ...restData } = data;
      response = await createQuestionBank(
        chapter ? { ...restData, chapter } : (restData as any)
      );
    }
    if (response.success) {
      fetchQuestions(currentPage, appliedFilters);
      setIsDialogOpen(false);
      reset({
        ...defaultFormValues,
        medium: dialogMedium,
        level: dialogLevel,
        standard: dialogStandard,
        subject: dialogSubject,
      });
      setEditingQuestion(null);
      toast.success(
        editingQuestion
          ? "Question updated successfully!"
          : "Question created successfully!"
      );
    } else {
      setApiError(response.error || "Failed to save question");
      toast.error(response.error || "Failed to save question");
    }
    setIsSubmitting(false);
  };

  const handleEdit = (question: QuestionBank) => {
    setEditingQuestion(question);
    const {
      question: q,
      optionOne,
      optionTwo,
      optionThree,
      optionFour,
      correctAnswer,
      medium,
      level,
      standard,
      subject,
      chapter,
    } = question;
    setDialogMedium(medium as "ENGLISH" | "GUJARATI");
    setDialogLevel(level as "EASY" | "MEDIUM" | "HARD");
    setDialogStandard(standard);
    setDialogSubject(subject);
    reset({
      question: q,
      optionOne,
      optionTwo,
      optionThree,
      optionFour,
      correctAnswer,
      medium: medium as "ENGLISH" | "GUJARATI",
      level: level as "EASY" | "MEDIUM" | "HARD",
      standard,
      subject,
      chapter: chapter || undefined,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (id: string) => {
    setDeleteQuestionId(id);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (deleteQuestionId) {
      const response = await deleteQuestionBank(deleteQuestionId);
      if (response.success) {
        fetchQuestions(currentPage, appliedFilters);
        toast.success("Question deleted successfully!");
      } else {
        toast.error(response.error || "Failed to delete question");
      }
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      reset({
        ...defaultFormValues,
        medium: dialogMedium,
        level: dialogLevel,
        standard: dialogStandard,
        subject: dialogSubject,
        chapter: undefined,
      });
      setEditingQuestion(null);
      setApiError(null);
    } else if (!editingQuestion) {
      reset({
        ...defaultFormValues,
        medium: dialogMedium,
        level: dialogLevel,
        standard: dialogStandard,
        subject: dialogSubject,
        chapter: undefined,
      });
    }
  };

  const handleAddQuestion = () => {
    setEditingQuestion(null);
    reset({
      ...defaultFormValues,
      medium: dialogMedium,
      level: dialogLevel,
      standard: dialogStandard,
      subject: dialogSubject,
      chapter: undefined,
    });
    setIsDialogOpen(true);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchQuestions(page, appliedFilters);
  };

  const handleDownloadTemplate = () => {
    const link = document.createElement("a");
    link.href = "/Sample_Questions.xlsx";
    link.setAttribute("download", "Sample_Questions.xlsx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "application/octet-stream",
      ];
      const isValidExtension =
        file.name.toLowerCase().endsWith(".xlsx") ||
        file.name.toLowerCase().endsWith(".xls");
      const isValidType = validTypes.includes(file.type) || file.type === "";
      if (!isValidExtension && !isValidType) {
        toast.error("Please select a valid Excel file (.xlsx or .xls)");
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleUploadExcel = async () => {
    if (!selectedFile) {
      toast.error("Please select a file to upload");
      return;
    }
    setIsUploading(true);
    try {
      const response = await uploadQuestionBankExcel(selectedFile);
      if (response.success) {
        const data = response.data;
        if (data.invalidQuestions > 0) {
          toast.success(
            `Upload completed! ${data.uploadedCount} questions imported successfully. ${data.invalidQuestions} questions were skipped due to invalid format.`,
            { duration: 5000 }
          );
        } else {
          toast.success(
            `Excel file uploaded successfully! ${data.uploadedCount} questions imported.`
          );
        }
        setImportDialog(false);
        setSelectedFile(null);
        fetchQuestions(currentPage, appliedFilters);
      } else {
        toast.error(response.error || "Failed to upload Excel file");
      }
    } catch (error) {
      toast.error("Failed to upload Excel file");
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const columns: ColumnDef<QuestionBank>[] = [
    {
      id: "select",
      header: () => (
        <Checkbox
          checked={
            selectedQuestions.length === questions.length &&
            questions.length > 0
          }
          onCheckedChange={(value) => {
            setSelectedQuestions(value ? questions.map((q) => q.id) : []);
          }}
          aria-label="Select all questions"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedQuestions.includes(row.original.id)}
          onCheckedChange={(value) =>
            handleCheckboxChange(row.original.id, !!value)
          }
          aria-label="Select question"
        />
      ),
    },
    {
      accessorKey: "question",
      header: "Question",
      cell: ({ row }) => (
        <div dangerouslySetInnerHTML={{ __html: row.getValue("question") }} />
      ),
    },
    { accessorKey: "optionOne", header: "Option 1" },
    { accessorKey: "optionTwo", header: "Option 2" },
    { accessorKey: "optionThree", header: "Option 3" },
    { accessorKey: "optionFour", header: "Option 4" },
    { accessorKey: "correctAnswer", header: "Correct Answer" },
    { accessorKey: "medium", header: "Medium" },
    { accessorKey: "standard", header: "Standard" },
    { accessorKey: "subject", header: "Subject" },
    { accessorKey: "level", header: "Level" },
    { accessorKey: "chapter", header: "Chapter" },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
            aria-label="Edit question"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-red-500 hover:text-red-700 hover:bg-red-100"
            onClick={() => handleDelete(row.original.id)}
            aria-label="Delete question"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Question Bank</h1>
        <div className="flex gap-3">
          <Button
            variant="destructive"
            onClick={handleBulkDelete}
            disabled={isLoading || selectedQuestions.length === 0}
          >
            Delete Bulk
          </Button>
          <Dialog open={importDialog} onOpenChange={setImportDialog}>
            <DialogTrigger asChild>
              <Button
                aria-label="Import Excel file"
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Import Excel
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Import Questions from Excel</DialogTitle>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                <div className="flex flex-col gap-3">
                  <h3 className="text-sm font-medium text-gray-900">
                    Step 1: Download Template
                  </h3>
                  <Button
                    variant="outline"
                    onClick={handleDownloadTemplate}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download Excel Template
                  </Button>
                  <p className="text-sm text-gray-600">
                    Download the template Excel file with the correct format for
                    your questions.
                  </p>
                </div>
                <div className="border-t pt-4">
                  <div className="flex flex-col gap-3">
                    <h3 className="text-sm font-medium text-gray-900">
                      Step 2: Upload Filled Excel File
                    </h3>
                    <div className="flex flex-col gap-2">
                      <input
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={handleFileSelect}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 border border-gray-300 rounded-md"
                      />
                      {selectedFile && (
                        <p className="text-sm text-green-600">
                          Selected: {selectedFile.name}
                        </p>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">
                      Upload your Excel file (.xlsx) with questions formatted
                      according to the template.
                    </p>
                  </div>
                </div>
              </div>
              <DialogFooter className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setImportDialog(false)}
                  disabled={isUploading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUploadExcel}
                  disabled={!selectedFile || isUploading}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  {isUploading ? "Uploading..." : "Upload Excel"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <Button
            onClick={handleDownloadExcel}
            disabled={isDownloading || isLoading}
            className="flex items-center gap-2 bg-[#ff914d] text-white hover:bg-[#e8823d] disabled:opacity-50"
            aria-label="Download Excel file"
          >
            <Download className="h-4 w-4" />
            {isDownloading ? "Downloading..." : "Download xlsx"}
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
            <DialogTrigger asChild>
              <Button onClick={handleAddQuestion} aria-label="Add new question">
                Add Question
              </Button>
            </DialogTrigger>
            <DialogContent className="max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingQuestion ? "Update Question" : "Create Question"}
                </DialogTitle>
              </DialogHeader>
              {fetchError ? (
                <p className="text-red-500 text-sm">
                  Cannot load form due to missing data: {fetchError}
                </p>
              ) : (
                <>
                  {apiError && (
                    <p className="text-red-500 text-sm">{apiError}</p>
                  )}
                  <div className="flex-1 overflow-y-auto pr-2 space-y-4">
                    <form
                      onSubmit={handleSubmit(onSubmit)}
                      className="space-y-4"
                    >
                      <div>
                        <label className="block text-sm font-medium">
                          Question
                        </label>
                        <Controller
                          name="question"
                          control={control}
                          render={({ field }) => (
                            <ReactQuill
                              theme="snow"
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Enter your question here..."
                            />
                          )}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium">
                            Option 1
                          </label>
                          <Input
                            {...register("optionOne")}
                            placeholder="Enter option 1"
                            disabled={isFetchingConstants || isSubmitting}
                            aria-label="Option 1 input"
                          />
                          {errors.optionOne && (
                            <p className="text-red-500 text-sm">
                              {errors.optionOne.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium">
                            Option 2
                          </label>
                          <Input
                            {...register("optionTwo")}
                            placeholder="Enter option 2"
                            disabled={isFetchingConstants || isSubmitting}
                            aria-label="Option 2 input"
                          />
                          {errors.optionTwo && (
                            <p className="text-red-500 text-sm">
                              {errors.optionTwo.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium">
                            Option 3
                          </label>
                          <Input
                            {...register("optionThree")}
                            placeholder="Enter option 3"
                            disabled={isFetchingConstants || isSubmitting}
                            aria-label="Option 3 input"
                          />
                          {errors.optionThree && (
                            <p className="text-red-500 text-sm">
                              {errors.optionThree.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium">
                            Option 4
                          </label>
                          <Input
                            {...register("optionFour")}
                            placeholder="Enter option 4"
                            disabled={isFetchingConstants || isSubmitting}
                            aria-label="Option 4 input"
                          />
                          {errors.optionFour && (
                            <p className="text-red-500 text-sm">
                              {errors.optionFour.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium">
                            Medium
                          </label>
                          <Controller
                            name="medium"
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  setDialogMedium(
                                    value as "ENGLISH" | "GUJARATI"
                                  );
                                }}
                                value={field.value}
                                disabled={isFetchingConstants || isSubmitting}
                              >
                                <SelectTrigger aria-label="Medium selector">
                                  <SelectValue placeholder="Select medium" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="ENGLISH">
                                    English
                                  </SelectItem>
                                  <SelectItem value="GUJARATI">
                                    Gujarati
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {errors.medium && (
                            <p className="text-red-500 text-sm">
                              {errors.medium.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium">
                            Level
                          </label>
                          <Controller
                            name="level"
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  setDialogLevel(
                                    value as "EASY" | "MEDIUM" | "HARD"
                                  );
                                }}
                                value={field.value}
                                disabled={isFetchingConstants || isSubmitting}
                              >
                                <SelectTrigger aria-label="Level selector">
                                  <SelectValue placeholder="Select level" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="EASY">Easy</SelectItem>
                                  <SelectItem value="MEDIUM">Medium</SelectItem>
                                  <SelectItem value="HARD">Hard</SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {errors.level && (
                            <p className="text-red-500 text-sm">
                              {errors.level.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium">
                            Standard
                          </label>
                          <Controller
                            name="standard"
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  setDialogStandard(value);
                                }}
                                value={field.value}
                                disabled={
                                  isFetchingConstants ||
                                  isSubmitting ||
                                  classrooms.length === 0
                                }
                              >
                                <SelectTrigger aria-label="Standard selector">
                                  <SelectValue
                                    placeholder={
                                      isFetchingConstants
                                        ? "Loading standards..."
                                        : classrooms.length === 0
                                        ? "No standards available"
                                        : "Select standard"
                                    }
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {classrooms.length > 0 ? (
                                    classrooms.map((classroom) => (
                                      <SelectItem
                                        key={classroom}
                                        value={classroom}
                                      >
                                        {classroom}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="no-standards" disabled>
                                      No standards available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {errors.standard && (
                            <p className="text-red-500 text-sm">
                              {errors.standard.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium">
                            Subject
                          </label>
                          <Controller
                            name="subject"
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  setDialogSubject(value);
                                }}
                                value={field.value}
                                disabled={
                                  isFetchingConstants ||
                                  isSubmitting ||
                                  subjects.length === 0
                                }
                              >
                                <SelectTrigger aria-label="Subject selector">
                                  <SelectValue
                                    placeholder={
                                      isFetchingConstants
                                        ? "Loading subjects..."
                                        : subjects.length === 0
                                        ? "No subjects available"
                                        : "Select subject"
                                    }
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {subjects.length > 0 ? (
                                    subjects.map((subject) => (
                                      <SelectItem key={subject} value={subject}>
                                        {subject}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="no-subjects" disabled>
                                      No subjects available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {errors.subject && (
                            <p className="text-red-500 text-sm">
                              {errors.subject.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium">
                            Correct Answer
                          </label>
                          <Controller
                            name="correctAnswer"
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={isFetchingConstants || isSubmitting}
                              >
                                <SelectTrigger aria-label="Correct answer selector">
                                  <SelectValue placeholder="Select correct answer" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="optionOne">
                                    Option 1
                                  </SelectItem>
                                  <SelectItem value="optionTwo">
                                    Option 2
                                  </SelectItem>
                                  <SelectItem value="optionThree">
                                    Option 3
                                  </SelectItem>
                                  <SelectItem value="optionFour">
                                    Option 4
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {errors.correctAnswer && (
                            <p className="text-red-500 text-sm">
                              {errors.correctAnswer.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium">
                            Chapter
                          </label>
                          <Controller
                            name="chapter"
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={field.onChange}
                                value={field.value || ""}
                                disabled={isFetchingConstants || isSubmitting}
                              >
                                <SelectTrigger aria-label="Chapter selector">
                                  <SelectValue placeholder="Select chapter" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Polynomial">
                                    Polynomial
                                  </SelectItem>
                                  <SelectItem value="Statics">
                                    Statics
                                  </SelectItem>
                                  <SelectItem value="Probability">
                                    Probability
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {errors.chapter && (
                            <p className="text-red-500 text-sm">
                              {errors.chapter.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => handleDialogOpenChange(false)}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          disabled={isFetchingConstants || isSubmitting}
                        >
                          {isSubmitting ? "Saving..." : "Save"}
                        </Button>
                      </DialogFooter>
                    </form>
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <ConfirmDialog
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
        title="Are you sure?"
        description="This action cannot be undone. This will permanently delete the question."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        isLoading={isLoading}
      />
      <ConfirmDialog
        open={isBulkDeleteDialogOpen}
        setOpen={setIsBulkDeleteDialogOpen}
        title="Are you sure?"
        description={`This action cannot be undone. This will permanently delete ${selectedQuestions.length} selected question(s).`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleBulkDeleteConfirm}
        isLoading={isLoading}
      />
      <div className="flex flex-col gap-5 mb-4 w-full">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-5 w-full">
          <div className="w-full">
            <label className="block text-sm font-medium">Medium</label>
            <Select
              onValueChange={(value: "ENGLISH" | "GUJARATI" | "ALL") =>
                setSelectedMedium(value)
              }
              value={selectedMedium}
              disabled={isFetchingConstants}
            >
              <SelectTrigger aria-label="Medium selector" className="w-full">
                <SelectValue placeholder="Select medium" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All</SelectItem>
                <SelectItem value="ENGLISH">English</SelectItem>
                <SelectItem value="GUJARATI">Gujarati</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full">
            <label className="block text-sm font-medium">Level</label>
            <Select
              onValueChange={(value: "EASY" | "MEDIUM" | "HARD" | "ALL") =>
                setSelectedLevel(value)
              }
              value={selectedLevel}
              disabled={isFetchingConstants}
            >
              <SelectTrigger aria-label="Level selector" className="w-full">
                <SelectValue placeholder="Select level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All</SelectItem>
                <SelectItem value="EASY">Easy</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="HARD">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full">
            <label className="block text-sm font-medium">Standard</label>
            <Select
              onValueChange={(value: string) => setSelectedStandard(value)}
              value={selectedStandard}
              disabled={isFetchingConstants || classrooms.length === 0}
            >
              <SelectTrigger aria-label="Standard selector" className="w-full">
                <SelectValue
                  placeholder={
                    isFetchingConstants
                      ? "Loading standards..."
                      : classrooms.length === 0
                      ? "No standards available"
                      : "Select standard"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {classrooms.length > 0 ? (
                  classrooms.map((classroom) => (
                    <SelectItem key={classroom} value={classroom}>
                      {classroom}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-standards" disabled>
                    No standards available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full">
            <label className="block text-sm font-medium">Subject</label>
            <Select
              onValueChange={(value: string) => setSelectedSubject(value)}
              value={selectedSubject}
              disabled={isFetchingConstants || subjects.length === 0}
            >
              <SelectTrigger aria-label="Subject selector" className="w-full">
                <SelectValue
                  placeholder={
                    isFetchingConstants
                      ? "Loading subjects..."
                      : subjects.length === 0
                      ? "No subjects available"
                      : "Select subject"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {subjects.length > 0 ? (
                  subjects.map((subject) => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-subjects" disabled>
                    No subjects available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex gap-5 justify-start">
          <Button
            onClick={handleSearch}
            disabled={isFetchingConstants || isLoading}
          >
            Search
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isFetchingConstants || isLoading}
          >
            Reset
          </Button>
        </div>
      </div>
      <DataTable
        columns={columns}
        data={questions}
        isLoading={isLoading}
      />
      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-600">{totalQuestions} entries</div>
        <div className="flex items-center gap-2">
          <Select
            value={limit.toString()}
            onValueChange={(value) => {
              setLimit(Number(value));
              setCurrentPage(1);
              fetchQuestions(1, appliedFilters);
            }}
          >
            <SelectTrigger
              className="w-[100px]"
              aria-label="Select number of questions per page"
            >
              <SelectValue placeholder="Select limit" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
              <SelectItem value="200">200</SelectItem>
              <SelectItem value="500">500</SelectItem>
              <SelectItem value="1000">1000</SelectItem>
            </SelectContent>
          </Select>
          <Pagination
            page={currentPage}
            totalPages={totalPages}
            setPage={handlePageChange}
            entriesText={`${questions.length} entries`}
          />
        </div>
      </div>
    </div>
  );
}
