"use client";

import { useEffect, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import axiosInstance from "@/lib/axios";
import { DataTable } from "@/app-components/dataTable";

type Ranking = {
  rank: number;
  userId: number;
  name: string;
  score: number;
  attempted: number;
  totalQuestions: number;
};

export default function ExamRankingPage() {
  const [data, setData] = useState<Ranking[]>([]);
  const params = useParams();
  const examId = params.examId;

  useEffect(() => {
    if (!examId) return;

    axiosInstance
      .get<Ranking[]>(`/ranking/${examId}`)
      .then((res) => setData(res.data))
      .catch((err) => console.error("Error fetching rankings:", err));
  }, [examId]);

  const columns: ColumnDef<Ranking, unknown>[] = [
    {
      accessorKey: "rank",
      header: "Rank",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "score",
      header: "Score",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "attempted",
      header: "Attempted",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "totalQuestions",
      header: "Total Questions",
      cell: (info) => info.getValue(),
    },
  ];

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Exam Rankings</h1>
      <DataTable
        columns={columns}
        data={data}
        isLoading={false}
      />
    </div>
  );
}
