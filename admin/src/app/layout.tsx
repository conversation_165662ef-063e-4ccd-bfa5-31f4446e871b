import type { <PERSON>ada<PERSON> } from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/sonner';
import { ReduxProvider } from './ReduxProvider';

export const metadata: Metadata = {
  title: 'Uest - Your Gateway to Educational Excellence',
  description: 'Your Gateway to Educational Excellence',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <ReduxProvider>{children}</ReduxProvider>
        <Toaster />
      </body>
    </html>
  );
}
