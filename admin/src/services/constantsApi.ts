import axiosInstance from '@/lib/axios';
import {
  ConstantCategory,
  ConstantDetail,
  ConstantSubDetail,
  ConstantSubDetailValue,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateDetailRequest,
  UpdateDetailRequest,
  CreateSubDetailRequest,
  UpdateSubDetailRequest,
  CreateValueRequest,
  UpdateValueRequest
} from '@/lib/types';

// Categories
export const getCategories = async (page: number = 1, limit: number = 10, search?: string): Promise<{ data: ConstantCategory[], pagination: any }> => {
  const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
  const response = await axiosInstance.get(`/admin/constants/categories?page=${page}&limit=${limit}${searchParam}`);
  return response.data.data;
};

export const getCategoryById = async (id: string): Promise<ConstantCategory> => {
  const response = await axiosInstance.get(`/admin/constants/categories/${id}`);
  return response.data.data;
};

export const createCategory = async (data: CreateCategoryRequest): Promise<ConstantCategory> => {
  const response = await axiosInstance.post('/admin/constants/categories', data);
  return response.data.data;
};

export const updateCategory = async (id: string, data: UpdateCategoryRequest): Promise<ConstantCategory> => {
  const response = await axiosInstance.put(`/admin/constants/categories/${id}`, data);
  return response.data.data;
};

export const deleteCategory = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/admin/constants/categories/${id}`);
};



// Details
export const getDetailsByCategory = async (categoryId: string, page: number = 1, limit: number = 10, search?: string): Promise<{ data: ConstantDetail[], pagination: any }> => {
  const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
  const response = await axiosInstance.get(`/admin/constants/details/category/${categoryId}?page=${page}&limit=${limit}${searchParam}`);
  return response.data.data;
};

export const getDetailById = async (id: string): Promise<ConstantDetail> => {
  const response = await axiosInstance.get(`/admin/constants/details/${id}`);
  return response.data.data;
};

export const createDetail = async (data: CreateDetailRequest): Promise<ConstantDetail> => {
  const response = await axiosInstance.post('/admin/constants/details', data);
  return response.data.data;
};

export const updateDetail = async (id: string, data: UpdateDetailRequest): Promise<ConstantDetail> => {
  const response = await axiosInstance.put(`/admin/constants/details/${id}`, data);
  return response.data.data;
};

export const deleteDetail = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/admin/constants/details/${id}`);
};



// Sub-Details
export const getSubDetailsByDetail = async (detailId: string, page: number = 1, limit: number = 10, search?: string): Promise<{ data: ConstantSubDetail[], pagination: any }> => {
  const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
  const response = await axiosInstance.get(`/admin/constants/sub-details/detail/${detailId}?page=${page}&limit=${limit}${searchParam}`);
  return response.data.data;
};

export const getSubDetailById = async (id: string): Promise<ConstantSubDetail> => {
  const response = await axiosInstance.get(`/admin/constants/sub-details/${id}`);
  return response.data.data;
};

export const createSubDetail = async (data: CreateSubDetailRequest): Promise<ConstantSubDetail> => {
  const response = await axiosInstance.post('/admin/constants/sub-details', data);
  return response.data.data;
};

export const updateSubDetail = async (id: string, data: UpdateSubDetailRequest): Promise<ConstantSubDetail> => {
  const response = await axiosInstance.put(`/admin/constants/sub-details/${id}`, data);
  return response.data.data;
};

export const deleteSubDetail = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/admin/constants/sub-details/${id}`);
};



// Values
export const getValuesBySubDetail = async (subDetailId: string, page: number = 1, limit: number = 10, search?: string): Promise<{ data: ConstantSubDetailValue[], pagination: any }> => {
  const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
  const response = await axiosInstance.get(`/admin/constants/values/sub-detail/${subDetailId}?page=${page}&limit=${limit}${searchParam}`);
  return response.data.data;
};

export const getValueById = async (id: string): Promise<ConstantSubDetailValue> => {
  const response = await axiosInstance.get(`/admin/constants/values/${id}`);
  return response.data.data;
};

export const createValue = async (data: CreateValueRequest): Promise<ConstantSubDetailValue> => {
  const response = await axiosInstance.post('/admin/constants/values', data);
  return response.data.data;
};

export const updateValue = async (id: string, data: UpdateValueRequest): Promise<ConstantSubDetailValue> => {
  const response = await axiosInstance.put(`/admin/constants/values/${id}`, data);
  return response.data.data;
};

export const deleteValue = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/admin/constants/values/${id}`);
};


