import { ClassesStudent } from "@/lib/types";
import axios from "axios";

const baseURL = process.env.NEXT_PUBLIC_CLASS_DASHBOARD_BASE_URL;
const API_KEY = process.env.NEXT_PUBLIC_SCHOOL_CREATION_API_KEY; 

interface PaginatedResponse {
  data: ClassesStudent[];
  total: number;
  current_page: number;
  last_page: number;
  per_page: number;
}

export const getStudentsByClassId = async (
  classId: string,
  page: number = 1,
  search: string = '',
  yearName: string = ''
): Promise<PaginatedResponse> => {
  try {
    const response = await axios.get(`${baseURL}/api/students-all-raw`, {
      params: { class_uuid: classId, page, search, year_name: yearName },
      headers: { 'X-API-KEY': API_KEY },
    });

    const students = response.data?.students;

    if (!students || !Array.isArray(students.data)) {
      throw new Error("Invalid students data format from API");
    }

    return {
      data: students.data,
      total: students.total,
      current_page: students.current_page,
      last_page: students.last_page,
      per_page: students.per_page
    };
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
      error.message ||
      "Failed to fetch students"
    );
  }
};

export const getUniqueYears = async (classId: string): Promise<string[]> => {
  try {
    const response = await axios.get(`${baseURL}/api/students-all-raw`, {
      params: { class_uuid: classId },
      headers: { 'X-API-KEY': API_KEY },
    });

    const students = response.data?.students?.data;

    if (!Array.isArray(students)) {
      throw new Error("Invalid students data format from API");
    }

    const years = [...new Set(students.map((student: ClassesStudent) => student.year_name))].filter(year => year) as string[];
    return years;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
      error.message ||
      "Failed to fetch years"
    );
  }
};