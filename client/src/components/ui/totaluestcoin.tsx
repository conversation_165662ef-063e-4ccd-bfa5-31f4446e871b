"use client";

import Image from "next/image";
import { motion } from "framer-motion";

interface TotalCoinsProps {
  totalCoins: number | null;
  badgeSrc: string | null;
  badgeAlt: string | null;
}

const TotalCoins: React.FC<TotalCoinsProps> = ({ totalCoins, badgeSrc, badgeAlt }) => {
  if (totalCoins === null) return null;

  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-1.5 text-base font-semibold text-yellow-600 bg-yellow-100 px-4 py-1.5 rounded-full shadow-sm">
        <Image src="/uest_coin.png" alt="Coin" width={20} height={20} className="w-5 h-5" />
        {totalCoins} Coins
      </div>
      {badgeSrc && badgeAlt && (
        <motion.div
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
          className="w-12 h-12 flex items-center justify-center"
        >
          <Image
            src={badgeSrc}
            alt={badgeAlt}
            width={48}
            height={48}
            className="object-contain"
          />
        </motion.div>
      )}
    </div>
  );
};

export default TotalCoins;