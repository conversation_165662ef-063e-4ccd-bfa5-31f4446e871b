"use client";

import { motion } from "framer-motion";
import StreakBadge from "../../../public/Streak";
import Image from "next/image";

interface Badge {
  badgeType: string;
  badgeSrc: string;
  badgeAlt: string;
  count?: number;
}

interface BadgeDisplayProps {
  badge: {
    streakCount: number;
    badges: Badge[];
    badgeType: string | null;
    badgeSrc: string | null;
    badgeAlt: string | null;
  } | null;
}

export default function BadgeDisplay({ badge }: BadgeDisplayProps) {
  if (!badge?.badges?.length) return null;

  return (
    <div className="flex gap-3 mt-2">
      {badge.badges.map((b, index) => (
        <motion.div
          key={index}
          className="relative"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, delay: index * 0.1 }}
        >
          {b.badgeType === "DailyStreak" ? (
            <StreakBadge count={b.count ?? 0} />
          ) : (
            <Image
              src={b.badgeSrc ?? "/placeholder.png"}
              alt={b.badgeAlt ?? "Badge"}
              width={48}
              height={48}
              className="object-contain sm:w-12 sm:h-12 w-10 h-10"
            />
          )}
        </motion.div>
      ))}
    </div>
  );
}