import React, { useState, useEffect } from 'react';
import { getMockExamStreak } from '@/services/mockExamStreakApi';


interface MockExamStreakResponse {
  success: boolean;
  data?: { streak: number; lastAttempt: string | null };
  error?: string;
}

interface StreakDisplayProps {
  studentId?: string;
}

const StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {
  const [streak, setStreak] = useState<number>(0);

  useEffect(() => {
    const fetchStreak = async () => {
      if (!studentId) {
        setStreak(0);
        return;
      }
      const response: MockExamStreakResponse = await getMockExamStreak(studentId);
      if (response.success && response.data) {
        setStreak(response.data.streak || 0);
      } else {
        setStreak(0);
      }
    };
    fetchStreak();
  }, [studentId]);

  return (
       <span className="bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1">
      🔥 {streak}
    </span>
  );
};

export default StreakDisplay;