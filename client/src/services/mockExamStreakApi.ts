import { axiosInstance } from "../lib/axios";

export const saveMockExamStreak = async (studentId: string): Promise<any> => {
  try {
    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to save mock exam streak: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};

export const getMockExamStreak = async (studentId: string): Promise<any> => {
  try {
    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data.data }; 
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam streak: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};