import { axiosInstance } from '@/lib/axios';

export const saveTerminatedStudent = async (data: any) => {
  try {
    const response = await axiosInstance.post('/mock-exam-terminate', data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to save termination log: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const countUwhizAttemp = async (studentId: string) => {
  try {
    const response = await axiosInstance.get(`mock-exam-terminate/count?studentId=${studentId}`,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed To Get Count of termination: ${error.response?.data?.message || error.message}`,
    };
  }
};