'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { getMockExamLeaderboard } from '@/services/LeaderboardUserApi';
import { Crown } from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import Image from 'next/image';
import { FaBolt } from 'react-icons/fa';
import BadgeDisplay from '@/components/ui/badgedisplay';

const tabs = ['Today', 'Weekly', 'All time'];

interface LeaderboardUser {
  rank: number;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakCount: number;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  badge: {
    streakCount: number;
    badges: {
      badgeType: string;
      badgeSrc: string;
      badgeAlt: string;
      count?: number;
    }[];
    badgeType: string | null;
    badgeSrc: string | null;
    badgeAlt: string | null;
  };
  profileImage?: string;
}

export default function LeaderboardPage() {
  const [selectedTab, setSelectedTab] = useState('Today');
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const limit = 10;

  const getBudgetIcon = (coinEarnings: number) => {
    if (coinEarnings >= 100 && coinEarnings <= 499) {
      return '/scholer.svg';
    } else if (coinEarnings >= 500 && coinEarnings <= 999) {
      return '/Mastermind.svg';
    } else if (coinEarnings >= 1000) {
      return '/Achiever.svg';
    }
    return null;
  };

  useEffect(() => {
    const fetchLeaderboard = async () => {
      setLoading(true);
      setError(null);
      const timeframe = selectedTab.toLowerCase().replace(' ', '-');
      const response = await getMockExamLeaderboard(timeframe, currentPage, limit);
      if (response.success && response.data?.data) {
        setLeaderboard(response.data.data);
        setTotalPages(Math.ceil(response.data.total / limit));
      } else {
        setError(response.error || 'Failed to fetch leaderboard');
      }
      setLoading(false);
    };
    fetchLeaderboard();
  }, [selectedTab, currentPage]);

  const topThree = leaderboard.slice(0, 3);
  const others = leaderboard.slice(3);

  const renderProfile = (user: LeaderboardUser, size: number = 96, isFirst: boolean = false) => {
    const profile = user.profileImage && user.profileImage.trim() !== '' ? (
      <Image
        src={user.profileImage}
        alt={`${user.firstName || ''} ${user.lastName || ''}`}
        width={size}
        height={size}
        className="rounded-full object-cover"
      />
    ) : (
      <div
        style={{ width: size, height: size }}
        className="flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-lg sm:text-xl md:text-2xl border-4 border-customOrange"
      >
        {(user.firstName?.charAt(0) || '') + (user.lastName?.charAt(0) || '')}
      </div>
    );

    return isFirst ? (
      <motion.div
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
      >
        {profile}
      </motion.div>
    ) : (
      profile
    );
  };

  return (
    <>
      <Header />
      <div className="min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center">
        <div className="w-full max-w-5xl space-y-6 sm:space-y-8 pt-8">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange">
            Daily Quiz Leaderboard
          </h1>
          <div className="flex justify-center gap-4 sm:gap-10 overflow-x-auto">
            {tabs.map((tab) => (
              <Button
                key={tab}
                variant={selectedTab === tab ? 'default' : 'outline'}
                className={`rounded-full px-4 sm:px-6 py-1 sm:py-2 text-sm sm:text-base font-semibold ${
                  selectedTab === tab ? 'bg-customOrange text-white' : 'border-orange-400 text-orange-400'
                } whitespace-nowrap`}
                aria-label={`Select ${tab} leaderboard`}
                onClick={() => {
                  setSelectedTab(tab);
                  setCurrentPage(1);
                }}
              >
                {tab}
              </Button>
            ))}
          </div>
          {loading && <p className="text-center text-gray-500">Loading...</p>}
          {error && <p className="text-center text-red-500">{error}</p>}
          {!loading && !error && (
            <div className="flex flex-col sm:flex-row justify-around items-center sm:items-end gap-4 sm:gap-6 mt-6 sm:mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg">
              {topThree.map((user, index) => (
                <motion.div
                  key={user.studentId}
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: index * 0.2 }}
                  className={`flex flex-col items-center ${
                    index === 0 ? 'order-2' : index === 1 ? 'order-1' : 'order-3'
                  }`}
                >
                  <div className="relative flex flex-col items-center">
                    <div
                      className={`relative rounded-full border-4 p-2 ${
                        index === 0 ? 'shadow-2xl scale-110 border-customOrange' : 'border-orange-500'
                      }`}
                    >
                      {index === 0 && (
                        <Crown
                          className="absolute -top-6 sm:-top-8 left-1/2 -translate-x-1/2 text-customOrange w-6 sm:w-8 h-6 sm:h-8"
                        />
                      )}
                      {renderProfile(user, 64, index === 0)}
                      <div
                        className={`absolute -bottom-4 sm:-bottom-5 left-1/2 -translate-x-1/2 rounded-full flex items-center justify-center font-bold ${
                          index === 0
                            ? 'w-7 h-7 sm:w-9 sm:h-9 bg-orange-500 text-white shadow-lg border-4 border-orange-500'
                            : index === 1
                            ? 'w-6 h-6 sm:w-8 sm:h-8 bg-orange-500 text-white shadow border-4 border-orange-500'
                            : 'w-5 h-5 sm:w-7 sm:h-7 bg-orange-500 text-white border-4 border-orange-500'
                        }`}
                      >
                        {user.rank}
                      </div>
                    </div>
                    <p className="mt-6 sm:mt-8 font-semibold text-base sm:text-lg text-center">
                      {user.firstName} {user.lastName}
                    </p>
                    <div className="mt-2 w-full flex justify-center">
                      <div className="flex items-center justify-center gap-2 sm:gap-3">
                        {getBudgetIcon(user.coinEarnings) && (
                          <div className="pt-1 sm:pt-2 flex items-center gap-1">
                            <Image
                              src={getBudgetIcon(user.coinEarnings)!}
                              alt="Budget Icon"
                              width={40}
                              height={40}
                              sizes="(max-width: 640px) 40px, 48px"
                              className="h-10 w-10 sm:h-12 sm:w-12 object-contain"
                              loading="lazy"
                            />
                          </div>
                        )}
                        <BadgeDisplay badge={user.badge} />
                      </div>
                    </div>
                    <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-5 mt-2">
                      <div
                        className={`px-3 sm:px-4 py-1 rounded-full border border-orange-300 text-orange-600 font-bold text-xs sm:text-sm ${
                          index === 0
                            ? 'bg-green-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]'
                            : 'bg-green-100'
                        }`}
                      >
                        <div className="flex items-center">
                          <FaBolt /> {user.score}
                        </div>
                      </div>
                      <div
                        className={`px-3 sm:px-4 py-1 rounded-full border border-green-300 text-green-600 font-bold text-xs sm:text-sm flex items-center gap-1 ${
                          index === 0
                            ? 'bg-green-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]'
                            : 'bg-green-100'
                        }`}
                      >
                        <Image
                          src="/uest_coin.png"
                          alt="Coin"
                          width={12}
                          height={12}
                          sizes="(max-width: 640px) 12px, 16px"
                          loading="lazy"
                        />
                        {user.coinEarnings}
                      </div>
                      <div
                        className={`px-3 sm:px-4 py-1 rounded-full border border-blue-300 text-blue-600 font-bold text-xs sm:text-sm ${
                          index === 0
                            ? 'bg-blue-100 animate-pulse shadow-[0_0_10px_2px_rgba(34,197,94,0.4)]'
                            : 'bg-blue-100'
                        }`}
                      >
                        🔥 {user.streakCount}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
          {!loading && !error && (
            <div className="rounded-lg mt-6 sm:mt-10 bg-white space-y-3 sm:space-y-4">
              {others.map((user: LeaderboardUser) => (
                <div
                  key={user.studentId}
                  className="flex flex-col sm:flex-row items-center justify-between p-3 sm:p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition"
                >
                  <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto">
                    <div className="relative flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-orange-100 text-orange-500 font-bold text-sm sm:text-lg">
                      {user.rank}
                    </div>
                    {renderProfile(user, 48)}
                    <p className="font-semibold text-base sm:text-lg text-black">
                      {user.firstName} {user.lastName}
                    </p>
                  </div>
                  <div className="flex flex-wrap justify-center items-center gap-3 sm:gap-5 mt-3 sm:mt-0 w-full sm:w-auto">
                    <div className="flex items-center gap-2 sm:gap-3">
                      {getBudgetIcon(user.coinEarnings) && (
                        <div className="pt-1 sm:pt-2 flex items-center gap-1">
                          <Image
                            src={getBudgetIcon(user.coinEarnings)!}
                            alt="Budget Icon"
                            width={40}
                            height={40}
                            sizes="(max-width: 640px) 40px, 48px"
                            className="h-10 w-10 sm:h-12 sm:w-12 object-contain"
                            loading="lazy"
                          />
                        </div>
                      )}
                      <BadgeDisplay badge={user.badge} />
                    </div>
                    <div className="flex items-center gap-2 sm:gap-3 flex-wrap justify-center">
                      <div className="min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-orange-300 bg-orange-100 text-orange-600">
                        <div className="flex items-center">
                          <FaBolt className="mr-1" /> {user.score}
                        </div>
                      </div>
                      <div className="min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-green-300 bg-green-100 text-green-700 flex items-center gap-1">
                        <Image
                          src="/uest_coin.png"
                          alt="Coin"
                          width={12}
                          height={12}
                          sizes="(max-width: 640px) 12px, 16px"
                          loading="lazy"
                        />
                        {user.coinEarnings}
                      </div>
                      <div className="min-w-[60px] sm:min-w-[68px] px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-bold border border-blue-300 bg-blue-100 text-blue-700">
                        🔥 {user.streakCount}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          {!loading && !error && leaderboard.length > 0 && (
            <div className="flex justify-center gap-4 mt-6 sm:mt-8">
              <Button
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((prev) => prev - 1)}
                className="px-4 sm:px-6 py-1 sm:py-2 rounded-full bg-customOrange text-white disabled:bg-gray-300 text-sm sm:text-base"
                aria-label="Go to previous page"
              >
                Previous
              </Button>
              <span className="flex items-center text-sm sm:text-lg">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage((prev) => prev + 1)}
                className="px-4 sm:px-6 py-1 sm:py-2 rounded-full bg-customOrange text-white disabled:bg-gray-300 text-sm sm:text-base"
                aria-label="Go to next page"
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </>
  );
}