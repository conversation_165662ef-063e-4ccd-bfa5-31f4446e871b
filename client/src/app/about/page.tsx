"use client";

import React from "react";
import { motion } from "framer-motion";
import { 
  GraduationCap, 
  Users, 
  Award,
  Lightbulb,
  Heart,
  TrendingUp,
  CheckCircle
} from "lucide-react";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import AuthErrorHand<PERSON> from "@/app-components/AuthErrorHandler";

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6 }
};

const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6 }
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function AboutPage() {
  return (
    <>
      <AuthErrorHandler />
      <Header />
      
      <div className="min-h-screen bg-white font-sans text-black antialiased">
        {/* Hero Section */}
        <section className="relative py-24 bg-gradient-to-b from-white to-orange-50 overflow-hidden">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 opacity-10">
            <motion.div 
              className="absolute top-20 left-1/4 w-32 h-32 rounded-full bg-orange-300 blur-[80px]"
              animate={{ 
                y: [0, -20, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div 
              className="absolute bottom-20 right-1/4 w-40 h-40 rounded-full bg-orange-400 blur-[90px]"
              animate={{ 
                y: [0, 20, 0],
                scale: [1, 0.9, 1]
              }}
              transition={{ 
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            />
            <motion.div 
              className="absolute top-1/3 right-10 w-24 h-24 rounded-full bg-amber-300 blur-[70px]"
              animate={{ 
                y: [0, -15, 0],
                x: [0, 10, 0]
              }}
              transition={{ 
                duration: 7,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
          </div>

          <div className="max-w-7xl mx-auto px-6 lg:px-8 grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
            {/* Content Column */}
            <motion.div 
              className="relative z-10 text-center md:text-left space-y-6"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              <motion.div 
                className="inline-flex items-center px-4 py-2 bg-orange-100 rounded-full mb-4"
                variants={fadeInUp}
              >
                <GraduationCap className="w-4 h-4 mr-2 text-orange-600" />
                <span className="text-sm font-medium text-orange-700">Educational Excellence</span>
              </motion.div>
              
              <motion.h1 
                className="text-5xl md:text-6xl font-bold tracking-tight text-gray-900"
                variants={fadeInUp}
              >
                <span className="relative whitespace-nowrap">
                  <span className="relative bg-gradient-to-r from-orange-500 to-amber-600 bg-clip-text text-transparent">
                    About UEST
                  </span>
                </span>
              </motion.h1>
              
              <motion.div 
                className="prose prose-lg text-gray-600 max-w-3xl space-y-4"
                variants={fadeInUp}
              >
                <p className="leading-relaxed text-lg">
                  At <span className="font-semibold text-orange-600">UEST</span>, we're revolutionizing education by connecting passionate learners with exceptional educators. Our platform transforms traditional learning into an engaging, personalized journey that adapts to every student's unique needs.
                </p>
                <p className="leading-relaxed">
                  From academic excellence to skill development, we provide comprehensive educational solutions including tutoring services, interactive quizzes, mock exams, and certification programs that prepare students for real-world success.
                </p>
                <p className="leading-relaxed">
                  Join thousands of students who have discovered their potential through our innovative learning ecosystem designed for the digital age.
                </p>
              </motion.div>

              <motion.div 
                className="flex flex-wrap gap-4 pt-4"
                variants={fadeInUp}
              >
                <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-orange-100">
                  <Users className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium">10,000+ Students</span>
                </div>
                <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-orange-100">
                  <Award className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium">500+ Expert Tutors</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Image Column */}
            <motion.div
              className="relative z-10"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInRight}
            >
              <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
                <img
                  src="/aboutus.png"
                  alt="UEST Educational Platform - Students and Teachers"
                  className="w-full h-auto rounded-xl object-cover"
                  style={{ minHeight: '300px' }}
                />
              </div>
              <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-orange-400 rounded-xl opacity-20 -z-10"></div>
              <div className="absolute -top-6 -right-6 w-20 h-20 bg-orange-500 rounded-full opacity-20 -z-10"></div>
            </motion.div>
          </div>

          {/* Floating Elements */}
          <motion.div 
            className="absolute bottom-20 left-10 w-6 h-6 rounded-full bg-orange-400 opacity-30"
            animate={{ 
              y: [0, -10, 0],
              scale: [1, 1.2, 1]
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div 
            className="absolute top-1/4 right-20 w-8 h-8 rounded-full bg-amber-400 opacity-30"
            animate={{ 
              y: [0, 15, 0],
              scale: [1, 0.8, 1]
            }}
            transition={{ 
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
        </section>

        {/* Educational Impact Section */}
        <section className="py-20 px-6 bg-gradient-to-br from-orange-50 to-white">
          <div className="max-w-6xl mx-auto">
            <motion.div 
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Transforming Education</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our comprehensive platform empowers students and educators with cutting-edge tools for modern learning
              </p>
            </motion.div>

            <motion.div 
              className="grid grid-cols-1 md:grid-cols-3 gap-8"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              {[
                {
                  icon: <Lightbulb className="w-8 h-8 text-orange-600" />,
                  title: "Interactive Learning",
                  description: "Engaging quizzes, mock exams, and gamified experiences that make learning enjoyable and effective"
                },
                {
                  icon: <Heart className="w-8 h-8 text-orange-600" />,
                  title: "Personalized Tutoring",
                  description: "Connect with expert tutors who understand your learning style and adapt to your pace"
                },
                {
                  icon: <TrendingUp className="w-8 h-8 text-orange-600" />,
                  title: "Skill Development",
                  description: "From academics to life skills, we provide comprehensive learning paths for holistic growth"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-lg border border-orange-100 hover:shadow-xl transition-shadow duration-300"
                  variants={fadeInUp}
                >
                  <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>
      </div>
      
      <Footer />
    </>
  );
}
