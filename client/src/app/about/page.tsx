"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  GraduationCap,
  Users,
  Award,
  Lightbulb,
  Heart,
  TrendingUp,
  CheckCircle,
  Target,
  Globe,
  Rocket
} from "lucide-react";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import AuthError<PERSON>andler from "@/app-components/AuthErrorHandler";

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6 }
};

const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6 }
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function AboutPage() {
  return (
    <>
      <AuthError<PERSON>and<PERSON> />
      <Header />

      <div className="min-h-screen bg-white font-sans text-black antialiased">
        {/* 1. About UEST - Hero Section */}
        <section className="relative py-24 bg-gradient-to-b from-white to-orange-50 overflow-hidden">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 opacity-10">
            <motion.div
              className="absolute top-20 left-1/4 w-32 h-32 rounded-full bg-orange-300 blur-[80px]"
              animate={{
                y: [0, -20, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-20 right-1/4 w-40 h-40 rounded-full bg-orange-400 blur-[90px]"
              animate={{
                y: [0, 20, 0],
                scale: [1, 0.9, 1]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            />
            <motion.div
              className="absolute top-1/3 right-10 w-24 h-24 rounded-full bg-amber-300 blur-[70px]"
              animate={{
                y: [0, -15, 0],
                x: [0, 10, 0]
              }}
              transition={{
                duration: 7,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
            />
          </div>

          <div className="max-w-7xl mx-auto px-6 lg:px-8 grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
            {/* Content Column */}
            <motion.div
              className="relative z-10 text-center md:text-left space-y-6"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              <motion.div
                className="inline-flex items-center px-4 py-2 bg-orange-100 rounded-full mb-4"
                variants={fadeInUp}
              >
                <GraduationCap className="w-4 h-4 mr-2 text-orange-600" />
                <span className="text-sm font-medium text-orange-700">Educational Excellence</span>
              </motion.div>

              <motion.h1
                className="text-5xl md:text-6xl font-bold tracking-tight text-gray-900"
                variants={fadeInUp}
              >
                <span className="relative whitespace-nowrap">
                  <span className="relative bg-gradient-to-r from-orange-500 to-amber-600 bg-clip-text text-transparent">
                    About UEST
                  </span>
                </span>
              </motion.h1>

              <motion.div
                className="prose prose-lg text-gray-600 max-w-3xl space-y-4"
                variants={fadeInUp}
              >
                <p className="leading-relaxed text-lg">
                  At <span className="font-semibold text-orange-600">UEST</span>, we believe education is not just about books and exams — it's about unlocking potential, building confidence, and nurturing a lifelong love for learning.
                </p>
                <p className="leading-relaxed">
                  Founded with a passion to bridge the gap between students and high-quality education, UEST is more than a tutoring platform — it's a movement toward empowered, personalized, and accessible learning for all.
                </p>
                <p className="leading-relaxed">
                  In today's fast-paced world, every learner is unique. That's why we built UEST to help students find the perfect tutor who understands them, adapts to their learning style, and helps them grow beyond the classroom.
                </p>
              </motion.div>

              <motion.div
                className="flex flex-wrap gap-4 pt-4"
                variants={fadeInUp}
              >
                <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-orange-100">
                  <Users className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium">10,000+ Students</span>
                </div>
                <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-sm border border-orange-100">
                  <Award className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium">500+ Expert Tutors</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Image Column */}
            <motion.div
              className="relative z-10"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInRight}
            >
              <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
                <img
                  src="/aboutus.png"
                  alt="UEST Educational Platform - Students and Teachers"
                  className="w-full h-auto rounded-xl object-cover"
                  style={{ minHeight: '300px' }}
                />
              </div>
              <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-orange-400 rounded-xl opacity-20 -z-10"></div>
              <div className="absolute -top-6 -right-6 w-20 h-20 bg-orange-500 rounded-full opacity-20 -z-10"></div>
            </motion.div>
          </div>

          {/* Floating Elements */}
          <motion.div
            className="absolute bottom-20 left-10 w-6 h-6 rounded-full bg-orange-400 opacity-30"
            animate={{
              y: [0, -10, 0],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute top-1/4 right-20 w-8 h-8 rounded-full bg-amber-400 opacity-30"
            animate={{
              y: [0, 15, 0],
              scale: [1, 0.8, 1]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
        </section>

        {/* 2. Educational Impact Section */}
        <section className="py-20 px-6 bg-gradient-to-br from-orange-50 to-white">
          <div className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Transforming Education</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our comprehensive platform empowers students and educators with cutting-edge tools for modern learning
              </p>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-8"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              {[
                {
                  icon: <Lightbulb className="w-8 h-8 text-orange-600" />,
                  title: "Interactive Learning",
                  description: "Engaging quizzes, mock exams, and gamified experiences that make learning enjoyable and effective"
                },
                {
                  icon: <Heart className="w-8 h-8 text-orange-600" />,
                  title: "Personalized Tutoring",
                  description: "Connect with expert tutors who understand your learning style and adapt to your pace"
                },
                {
                  icon: <TrendingUp className="w-8 h-8 text-orange-600" />,
                  title: "Skill Development",
                  description: "From academics to life skills, we provide comprehensive learning paths for holistic growth"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-lg border border-orange-100 hover:shadow-xl transition-shadow duration-300"
                  variants={fadeInUp}
                >
                  <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* 3. Mission & Vision Section */}
        <section className="py-20 px-6 bg-gradient-to-b from-white to-orange-50">
          <div className="max-w-6xl mx-auto space-y-20">
            {/* Mission */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <motion.div
                className="order-1 md:order-1"
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={fadeInLeft}
              >
                <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
                  <img
                    src="/mission.png"
                    alt="UEST Mission - Connecting Students and Educators"
                    className="w-full h-auto rounded-xl object-cover"
                    style={{ minHeight: '300px' }}
                  />
                </div>
              </motion.div>

              <motion.div
                className="order-2 md:order-2"
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={fadeInRight}
              >
                <div className="flex items-center mb-6">
                  <div className="bg-orange-100 p-3 rounded-xl mr-4">
                    <Target className="h-8 w-8 text-orange-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-orange-600">Our Mission</h2>
                </div>

                <div className="space-y-6 text-gray-700">
                  <p className="text-lg leading-relaxed">
                    <strong className="text-orange-600">To democratize quality education</strong> by creating meaningful connections between passionate learners and exceptional educators through innovative technology and personalized learning experiences.
                  </p>

                  <ul className="space-y-4">
                    {[
                      "Personalized learning experiences that adapt to each student's unique needs and goals",
                      "Empowering tutors with tools to showcase their expertise and reach more students",
                      "Breaking down geographical barriers to make quality education accessible to all",
                      "Fostering confidence and lifelong learning beyond academic achievements"
                    ].map((item, index) => (
                      <li key={index} className="flex items-start">
                        <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                          <CheckCircle className="h-5 w-5 text-orange-600" />
                        </div>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            </div>

            {/* Vision */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <motion.div
                className="order-2 md:order-1"
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={fadeInLeft}
              >
                <div className="flex items-center mb-6">
                  <div className="bg-orange-100 p-3 rounded-xl mr-4">
                    <Globe className="h-8 w-8 text-orange-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-orange-600">Our Vision</h2>
                </div>

                <div className="space-y-6">
                  <blockquote className="text-gray-700 text-lg italic mb-6 border-l-4 border-orange-500 pl-4 py-2 bg-orange-50 rounded-r-lg">
                    "To create a world where every learner can access personalized education and every educator can share their knowledge without boundaries."
                  </blockquote>

                  <ul className="space-y-4 text-gray-700">
                    {[
                      "To become the most trusted global platform connecting learners and educators",
                      "To transform education through AI-powered personalization and interactive tools",
                      "To build a community where knowledge sharing is rewarding and impactful",
                      "To make quality education accessible regardless of location or economic background"
                    ].map((item, index) => (
                      <li key={index} className="flex items-start">
                        <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                          <CheckCircle className="h-5 w-5 text-orange-600" />
                        </div>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>

              <motion.div
                className="order-1 md:order-2"
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={fadeInRight}
              >
                <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
                  <img
                    src="/visionn.png"
                    alt="UEST Vision - Global Education Network"
                    className="w-full h-auto rounded-xl object-cover"
                    style={{ minHeight: '300px' }}
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* 4. Our Products Section */}
        <section className="py-20 px-6 bg-gradient-to-b from-orange-50 to-white">
          <div className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Our Educational Products</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Innovative solutions transforming education and learning experiences across different domains
              </p>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-10"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              {[
                {
                  name: "UEST",
                  subtitle: "Tutor Finder Platform",
                  description: "Connect with expert tutors across all subjects - from academics to music, languages, and professional skills. Find your perfect learning match.",
                  image: "/uestlogo.png",
                  features: ["1-on-1 Tutoring", "Group Classes", "Skill Development", "Flexible Scheduling"]
                },
                {
                  name: "UWhiz",
                  subtitle: "Interactive Quiz Platform",
                  description: "Engaging daily quizzes, competitive exams, and knowledge challenges designed to make learning fun and effective.",
                  image: "/uwhiz-logo.png",
                  features: ["Daily Quizzes", "Mock Exams", "Leaderboards", "Certificates"]
                },
                {
                  name: "Rann Dass",
                  subtitle: "School ERP System",
                  description: "Comprehensive school management platform handling admissions, attendance, exams, fees, and all administrative operations.",
                  image: "/randasslogo.jpg",
                  features: ["Student Management", "Fee Management", "Exam System", "Reports & Analytics"]
                }
              ].map((product, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-100 group"
                  variants={fadeInUp}
                >
                  <div className="h-48 bg-gray-50 flex items-center justify-center p-6">
                    <img
                      src={product.image}
                      alt={`${product.name} - ${product.subtitle}`}
                      className="h-32 object-contain group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-2xl font-bold text-gray-800 mb-2">{product.name}</h3>
                    <p className="text-orange-600 font-medium mb-4">{product.subtitle}</p>
                    <p className="text-gray-600 mb-4">{product.description}</p>
                    <div className="space-y-2">
                      {product.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-4 h-4 text-orange-500 mr-2" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* 5. Leadership Team Section */}
        <section className="py-20 px-6 bg-white">
          <div className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Founder and Chairman of UEST</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Meet the visionary leaders driving UEST's mission to transform education
              </p>
            </motion.div>

            {/* Chairman */}
            <motion.div
              className="max-w-5xl mx-auto flex flex-col md:flex-row items-center gap-8 bg-orange-50 p-8 rounded-xl shadow-lg border border-orange-200 mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <div className="w-full md:w-80 flex-shrink-0">
                <img
                  src="/c5078ec7b5679976947d90e4a19e1bbb.jpg"
                  alt="Mr. Khodaji Padsumbiya - Founder & Chairman"
                  className="w-full h-auto object-cover rounded-lg shadow-lg"
                />
              </div>
              <div className="text-left flex-1">
                <h3 className="text-3xl font-bold text-gray-900 mb-2">Mr. Khodaji Padsumbiya</h3>
                <p className="text-orange-600 font-semibold text-lg mb-4">Founder & Chairman</p>
                <p className="text-gray-700 leading-relaxed text-lg mb-6">
                  As the visionary behind UEST, Mr. Khodaji Padsumbiya laid the foundation for transforming education
                  through innovation. With bold leadership and a passion for change, he guides UEST towards global impact,
                  ensuring that quality education becomes accessible to learners everywhere.
                </p>
              </div>
            </motion.div>

            {/* CTO Section */}
            <motion.div
              className="text-center mb-12"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Chief Technology Officer</h2>
            </motion.div>

            <motion.div
              className="max-w-5xl mx-auto flex flex-col md:flex-row-reverse items-center gap-8 bg-orange-50 p-8 rounded-xl shadow-lg border border-orange-200 mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <div className="w-full md:w-80 flex-shrink-0">
                <img
                  src="/c5078ec7b5679976947d90e4a19e1bbb.jpg"
                  alt="Mr. Vivek Moradiya - Chief Technology Officer"
                  className="w-full h-auto object-cover rounded-lg shadow-lg"
                />
              </div>
              <div className="text-left flex-1">
                <h3 className="text-3xl font-bold text-gray-900 mb-2">Mr. Vivek Moradiya</h3>
                <p className="text-orange-600 font-semibold text-lg mb-4">Chief Technology Officer</p>
                <p className="text-gray-700 leading-relaxed text-lg mb-6">
                  With a strong technical background and strategic thinking, Mr. Vivek Moradiya ensures our platform remains secure,
                  scalable, and cutting-edge. He leads the technology team that powers UEST's mission to revolutionize education
                  through innovative digital solutions.
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* 6. Team Leaders */}
        <section className="py-20 px-6 bg-gradient-to-b from-orange-50 to-white">
          <div className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-12"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Team Leaders</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Experienced leaders guiding our specialized teams to excellence
              </p>
            </motion.div>

            <motion.div
              className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 mb-20"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              {[
                {
                  name: "Mr. Milan Bhimani",
                  title: "Team Leader - UI/UX",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  description: "Leading frontend innovation and user-centered design with expertise in modern UI/UX principles.",
                },
                {
                  name: "Ms. Ishva Visavadiya",
                  title: "Team Leader - Backend",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  description: "Architecting scalable and secure server-side systems that power our educational platform.",
                },
                {
                  name: "Mr. Harshad Tank",
                  title: "Team Leader - Mobile Dev",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  description: "Developing fast and modern mobile experiences that bring education to your fingertips.",
                },
              ].map((leader, index) => (
                <motion.div
                  key={index}
                  className="bg-orange-50 p-6 rounded-xl border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col items-center text-center group"
                  variants={fadeInUp}
                >
                  <div className="w-32 h-32 mb-4 overflow-hidden rounded-full border-4 border-orange-200 group-hover:border-orange-400 transition-colors">
                    <img
                      src={leader.image}
                      alt={leader.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-1">{leader.name}</h4>
                  <p className="text-orange-600 font-semibold mb-3">{leader.title}</p>
                  <p className="text-gray-700 text-sm leading-relaxed mb-4">{leader.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* 7. Development Team Section */}
        <section className="py-20 px-6 bg-white">
          <div className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-16"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={fadeInUp}
            >
              <h2 className="text-4xl font-bold text-orange-500 mb-4">Development Team</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Talented developers and designers bringing our educational vision to life
              </p>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={staggerContainer}
            >
              {[
                {
                  name: "Mr. Nikhil Faldu",
                  role: "Frontend Developer",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  skills: "React, Next.js, UI/UX"
                },
                {
                  name: "Mr. Sham Rathod",
                  role: "Backend Developer",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  skills: "Node.js, MongoDB, APIs"
                },
                {
                  name: "Ms. Jency Fultariya",
                  role: "Mobile Developer",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  skills: "React Native, Flutter"
                },
                {
                  name: "Ms. Drashti Khalpada",
                  role: "UI/UX Designer",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  skills: "Figma, Adobe XD, Prototyping"
                },
                {
                  name: "Ms. Kruti Dasadiya",
                  role: "Quality Assurance",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  skills: "Testing, Automation"
                },
                {
                  name: "Ms. Dhara Maru",
                  role: "DevOps Engineer",
                  image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                  skills: "AWS, CI/CD, Docker"
                }
              ].map((member, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-100 flex flex-col items-center text-center group"
                  variants={fadeInUp}
                >
                  <div className="w-24 h-24 mb-4 overflow-hidden rounded-full border-2 border-orange-200 group-hover:border-orange-400 transition-colors">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800 mb-1">{member.name}</h3>
                  <p className="text-orange-600 font-medium mb-2 text-sm">{member.role}</p>
                  <p className="text-gray-600 text-xs">{member.skills}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>