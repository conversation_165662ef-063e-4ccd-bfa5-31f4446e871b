'use client';

import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import React, { useState, useEffect, Suspense } from 'react';
import { Button } from '@/components/ui/button';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { toast } from 'sonner';
import { verifyOtp, resendOtp } from '@/services/studentAuthServices';
import { useRouter, useSearchParams } from 'next/navigation';

const otpSchema = z.object({
  otp: z.string().regex(/^\d{6}$/, 'OTP must be a 6-digit number'),
  contactNo: z.string().regex(/^\d{10}$/, 'Invalid mobile number'),
  email: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      'Invalid email address',
    ),
  firstName: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[a-zA-Z]+$/.test(val),
      'Invalid first name',
    ),
  lastName: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[a-zA-Z]+$/.test(val),
      'Invalid last name',
    ),
  referralCode: z.string().optional(),
});

type OtpFormValues = z.infer<typeof otpSchema>;

const FormErrorAlert = ({ message }: { message: string }) => {
  if (!message) return null;
  return (
    <Alert className="mb-4 border-red-500 bg-red-50 dark:bg-red-900/20">
      <AlertCircle className="h-4 w-4 text-red-500" />
      <AlertDescription className="text-red-500">{message}</AlertDescription>
    </Alert>
  );
};

function StudentOtpVerificationContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [authError, setAuthError] = useState<string>('');
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(120);

  const contactNo = searchParams.get('contactNo') || '';
  const email = searchParams.get('email') || '';
  const flow = searchParams.get('flow') || 'login';
  const firstName = searchParams.get('firstName') || '';
  const lastName = searchParams.get('lastName') || '';
  const referralCode = searchParams.get('referralCode') || '';
  const isRegistration = flow === 'register';

  const timerKey = `otpTimer_${contactNo}_${flow}`;

  const otpForm = useForm<OtpFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: '',
      contactNo,
      email,
      firstName,
      lastName,
      referralCode,
    },
    mode: 'onChange',
  });

  const { formState, trigger, setValue } = otpForm;

  // Validate initial params and clear stale timer
  useEffect(() => {
    if (!contactNo || !/^\d{10}$/.test(contactNo)) {
      setAuthError('Invalid mobile number. Please try again.');
      toast.error('Invalid mobile number. Redirecting to login.');
      setTimeout(() => router.push('/student-login'), 2000);
      return;
    }
    setValue('contactNo', contactNo, { shouldValidate: true });
    setValue('email', email, { shouldValidate: true });
    setValue('firstName', firstName, { shouldValidate: true });
    setValue('lastName', lastName, { shouldValidate: true });
    setValue('referralCode', referralCode, { shouldValidate: true });
    trigger();

    // Clear any stale timer from localStorage on page load
    localStorage.removeItem(timerKey);
    setTimeLeft(120); // Start fresh at 120 seconds
  }, [contactNo, email, firstName, lastName, referralCode, router, setValue, trigger, timerKey]);

  // Timer logic
  useEffect(() => {
    // Start timer if timeLeft > 0
    let interval: NodeJS.Timeout | null = null;
    if (timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prev) => {
          const newTime = prev - 1;
          if (newTime <= 0) {
            localStorage.removeItem(timerKey);
            clearInterval(interval!);
            return 0;
          }
          localStorage.setItem(timerKey, newTime.toString());
          return newTime;
        });
      }, 1000);
    }

    // Cleanup on unmount
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timerKey, timeLeft]);

  const onOtpSubmit = async (data: OtpFormValues) => {
    setIsSubmitting(true);
    setAuthError('');

    try {
      const response = await verifyOtp({
        contactNo: data.contactNo,
        otp: data.otp,
        ...(data.email && { email: data.email }),
        ...(isRegistration && {
          firstName: data.firstName,
          lastName: data.lastName,
          referralCode: data.referralCode,
        }),
      });

      if (response.success === false) {
        setAuthError(response.message || 'OTP verification failed');
        toast.error(response.message || 'OTP verification failed');
        return;
      }

      if (response.data) {
        const { userId, contactNo, firstName, lastName, token } = response.data;
        localStorage.setItem('studentToken', token);
        localStorage.setItem('student_data', JSON.stringify({ id: userId, contactNo, firstName, lastName }));
        localStorage.removeItem(timerKey); // Clear timer on successful verification
        toast.success(
          data.email && !isRegistration
            ? 'Contact number updated and login successful'
            : isRegistration
              ? 'Registration successful'
              : 'Login successful',
        );

        const redirect = searchParams.get('redirect') || '/';
        router.push(redirect);
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Something went wrong';
      setAuthError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOtp = async () => {
    setIsResending(true);
    setAuthError('');
    try {
      const response = await resendOtp({
        contactNo,
        firstName: firstName || 'User',
      });
      if (response.success === false) {
        setAuthError(response.message || 'Failed to resend OTP');
        toast.error(response.message || 'Failed to resend OTP');
        return;
      }
      // Reset timer only on successful resend
      setTimeLeft(120);
      localStorage.setItem(timerKey, '120');
      toast.success('New OTP sent successfully');
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Something went wrong';
      if (errorMessage.includes('Too many OTP requests')) {
        toast.error('Too many OTP requests. Please wait before trying again.');
      } else {
        toast.error(errorMessage);
      }
      setAuthError(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <>
      <Header />
      <main className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Verify OTP</h2>
            <p className="text-[#ff914d]">
              Enter the 6-digit OTP sent to {contactNo}
            </p>
            {email && <p className="text-gray-600 text-sm mt-1">Linked to email: {email}</p>}
          </div>

          <div>
            <Form {...otpForm}>
              <form onSubmit={otpForm.handleSubmit(onOtpSubmit)} className="space-y-6">
                {authError && <FormErrorAlert message={authError} />}
                <FormField
                  control={otpForm.control}
                  name="otp"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">OTP</FormLabel>
                      <FormControl>
                        <div className="flex space-x-2 justify-center">
                          {[...Array(6)].map((_, index) => (
                            <Input
                              key={index}
                              type="text"
                              maxLength={1}
                              className="w-12 h-12 text-center text-lg font-medium border-gray-200 focus:border-[#ff914d] focus:ring-[#ff914d]/20 rounded-lg"
                              value={field.value[index] || ''}
                              onChange={(e) => {
                                const newValue = (field.value || '').split('');
                                newValue[index] = e.target.value.replace(/\D/g, '');
                                field.onChange(newValue.join(''));
                                trigger('otp');
                                if (e.target.value && index < 5) {
                                  (document.getElementById(`otp-${index + 1}`) as HTMLInputElement)?.focus();
                                }
                              }}
                              onKeyDown={(e) => {
                                if (e.key === 'Backspace' && !field.value[index] && index > 0) {
                                  (document.getElementById(`otp-${index - 1}`) as HTMLInputElement)?.focus();
                                }
                              }}
                              id={`otp-${index}`}
                            />
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={otpForm.control}
                  name="contactNo"
                  render={({ field }) => (
                    <FormItem hidden>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={otpForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem hidden>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={otpForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem hidden>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={otpForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem hidden>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={otpForm.control}
                  name="referralCode"
                  render={({ field }) => (
                    <FormItem hidden>
                      <FormControl>
                        <Input {...field} value={field.value || ''} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white font-medium py-2.5 rounded-lg transition-colors"
                  disabled={isSubmitting || !formState.isValid}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    'Verify OTP'
                  )}
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full text-[#ff914d] hover:text-[#ff914d]/90"
                  disabled={isResending || timeLeft > 0}
                  onClick={handleResendOtp}
                >
                  {isResending ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : timeLeft > 0 ? (
                    `Resend OTP in ${timeLeft}s`
                  ) : (
                    'Resend OTP'
                  )}
                </Button>
              </form>
            </Form>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}

export default function StudentOtpVerificationPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      }
    >
      <StudentOtpVerificationContent />
    </Suspense>
  );
}