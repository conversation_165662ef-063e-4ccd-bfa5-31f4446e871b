export default function HomePage() {
  return (
    <div className="min-h-screen bg-white font-sans text-black antialiased">

      {/* Hero Section */}
<header className="relative py-24 bg-gradient-to-b from-white to-orange-50 overflow-hidden">
  {/* Background elements */}
  <div className="absolute inset-0 opacity-15">
    <div className="absolute top-20 left-1/4 w-32 h-32 rounded-full bg-orange-300 blur-[80px] animate-float"></div>
    <div className="absolute bottom-20 right-1/4 w-40 h-40 rounded-full bg-orange-400 blur-[90px] animate-float-delay"></div>
    <div className="absolute top-1/3 right-10 w-24 h-24 rounded-full bg-amber-300 blur-[70px] animate-float"></div>
  </div>

  {/* Grid container */}
  <div className="max-w-7xl mx-auto px-6 lg:px-8 grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
    {/* Content column */}
    <div className="relative z-10 text-center md:text-left space-y-6">
      <div className="inline-flex items-center px-4 py-2 bg-orange-100 rounded-full mb-4">
        <span className="text-sm font-medium text-orange-700">Our Story</span>
      </div>
      <h1 className="text-5xl md:text-6xl font-bold tracking-tight text-gray-900">
        <span className="relative whitespace-nowrap">
          <span className="relative bg-gradient-to-r from-orange-500 to-amber-600 bg-clip-text text-transparent">
            About Us
          </span>
        </span>
      </h1>
      
      <div className="prose prose-lg text-gray-600 max-w-3xl">
        <p className="leading-relaxed">
          At <span className="font-semibold text-orange-600">UEST</span>, we believe education is not just about books and exams — it's about unlocking potential, building confidence, and nurturing a lifelong love for learning.
        </p>
        <p className="leading-relaxed">
          Founded with a passion to bridge the gap between students and high-quality education, UEST is more than a tutoring platform — it's a movement toward empowered, personalized, and accessible learning for all.
        </p>
        <p className="leading-relaxed">
          In today's fast-paced world, every learner is unique. That's why we built UEST to help students find the perfect tutor who understands them, adapts to their learning style, and helps them grow beyond the classroom.
        </p>
      </div>
    </div>

    {/* Image column - kept normal */}
    <div className="relative z-10">
      <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
        <img 
          src="/aboutus.png" 
          alt="Our Team" 
          className="w-full h-auto rounded-xl object-cover"
          style={{ minHeight: '300px' }}
        />
      </div>
      <div className="absolute -bottom-6 -left-6 w-24 h-24 bg-orange-400 rounded-xl opacity-20 -z-10"></div>
      <div className="absolute -top-6 -right-6 w-20 h-20 bg-orange-500 rounded-full opacity-20 -z-10"></div>
    </div>
  </div>

  {/* Floating elements */}
  <div className="absolute bottom-20 left-10 w-6 h-6 rounded-full bg-orange-400 opacity-30 animate-float"></div>
  <div className="absolute top-1/4 right-20 w-8 h-8 rounded-full bg-amber-400 opacity-30 animate-float-delay"></div>
</header>
      {/* Journey Section */}
      <section id="journey" className="relative py-20 px-6 bg-gradient-to-br from-orange-50 to-white">
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-block mb-10">
            <h2 className="text-4xl font-bold text-orange-500 mb-3 relative inline-block">
              UEST Journey
              <span className="absolute bottom-0 left-0 w-full h-1 bg-orange-500 transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-500"></span>
            </h2>
            <div className="w-20 h-1 bg-orange-300 mx-auto mt-2"></div>
          </div>
          <p className="max-w-3xl mx-auto text-gray-700 text-lg leading-relaxed animate-fadeInUp">
            Our journey began with a vision to revolutionize learning. From early prototypes to a thriving platform,
            UEST has grown through challenges, innovation, and dedication. Every milestone reflects our passion for
            empowering education with technology.
          </p>
          <div className="mt-12 grid grid-cols-3 gap-4 max-w-2xl mx-auto">
            {[2019, 2021, 2023].map((year, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500 hover:shadow-lg transition-shadow duration-300">
                <div className="text-orange-600 font-bold">{year}</div>
                <div className="text-xs text-gray-500 mt-1">Milestone</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Goals Section */}
      <section id="goals" className="py-20 px-6 bg-gradient-to-br from-gray-900 to-black text-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-orange-500 mb-4">Our Goals</h2>
            <div className="w-20 h-1 bg-orange-300 mx-auto"></div>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {[
              {
                title: "Innovation in Education",
                text: "Keep evolving our platform to meet modern learning needs through creative and adaptive technology.",
                icon: "💡"
              },
              {
                title: "Global Reach",
                text: "Expand our learning tools and accessibility worldwide for all learners and educators.",
                icon: "🌍"
              },
              {
                title: "Empowering Community",
                text: "Build a space where learners and mentors grow together and share knowledge freely.",
                icon: "👥"
              },
              {
                title: "Sustainable Impact",
                text: "Ensure our solutions remain accessible, valuable, and eco-conscious for years to come.",
                icon: "♻️"
              },
            ].map((goal, index) => (
              <div
                key={index}
                className="bg-gray-800 p-6 rounded-xl border border-gray-700 hover:border-orange-500 transition-all duration-300 group hover:shadow-lg hover:shadow-orange-500/20"
              >
                <div className="text-3xl mb-4">{goal.icon}</div>
                <h3 className="text-xl font-bold text-orange-400 mb-3 group-hover:text-orange-300 transition-colors">{goal.title}</h3>
                <p className="text-gray-300">{goal.text}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      


      {/* Mission & Vision Section */}
<section className="py-20 px-6 bg-gradient-to-b from-orange-50 to-white">
  <div className="max-w-6xl mx-auto space-y-20">

    {/* Mission - Image Left, Content Right */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
      {/* Image Column */}
      <div className="order-1 md:order-1 animate-fadeIn">
        <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
          <img 
            src="/mission.png" 
            alt="UEST tutoring platform connecting students and teachers" 
            className="w-full h-auto rounded-xl object-cover"
            style={{ minHeight: '300px' }}
          />
        </div>
      </div>

      {/* Content Column */}
      <div className="order-2 md:order-2">
        <div className="flex items-center mb-6">
          <div className="bg-orange-100 p-3 rounded-xl mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-orange-600">Our Mission</h2>
        </div>
        
        <div className="space-y-6 text-gray-700">
          <p className="text-lg leading-relaxed">
            <strong className="text-orange-600">To revolutionize education</strong> by creating meaningful connections between students and exceptional tutors through innovative technology.
          </p>
          
          <ul className="space-y-4">
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Personalized learning experiences that adapt to each student's unique needs and goals</span>
            </li>
            
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Empowering tutors with tools to showcase their expertise and reach more students</span>
            </li>
            
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Breaking down geographical barriers to make quality education accessible to all</span>
            </li>
            
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Fostering confidence and lifelong learning beyond academic achievements</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    {/* Vision - Content Left, Image Right */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
      {/* Content Column */}
      <div className="order-2 md:order-1">
        <div className="flex items-center mb-6">
          <div className="bg-orange-100 p-3 rounded-xl mr-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-orange-600">Our Vision</h2>
        </div>
        
        <div className="space-y-6">
          <blockquote className="text-gray-700 text-lg italic mb-6 border-l-4 border-orange-500 pl-4 py-2 bg-orange-50 rounded-r-lg">
            "To create a world where every learner can access personalized education and every educator can share their knowledge without boundaries."
          </blockquote>
          
          <ul className="space-y-4 text-gray-700">
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span>To become the most trusted global platform connecting learners and educators</span>
            </li>
            
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span>To transform education through AI-powered personalization and interactive tools</span>
            </li>
            
            <li className="flex items-start">
              <div className="bg-orange-100 p-1 rounded-full mr-3 mt-0.5">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span>To build a community where knowledge sharing is rewarding and impactful</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Image Column */}
      <div className="order-1 md:order-2 animate-fadeIn">
        <div className="bg-white p-4 rounded-2xl shadow-xl border border-orange-100">
          <img 
            src="/visionn.png" 
            alt="Global education network vision" 
            className="w-full h-auto rounded-xl object-cover"
            style={{ minHeight: '300px' }}
          />
        </div>
      </div>
    </div>

  </div>
</section>
      {/* Products Section */}
      <section className="py-20 px-6 bg-gradient-to-b from-white to-orange-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-orange-500 text-center mb-4">Our Products</h2>
          <p className="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
            Innovative solutions transforming education and learning experiences
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {/* Rann Dass - School ERP */}
            <div className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 border border-orange-100">
              <div className="h-48 bg-gray-100 flex items-center justify-center">
                <img
                  src="/randasslogo.jpg"
                  alt="Rann Dass School ERP"
                  className="h-32 object-contain"
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Rann Dass</h3>
                <p className="text-orange-600 font-medium mb-4">School ERP System</p>
                <p className="text-gray-600">
                  Comprehensive school management platform handling admissions, attendance,
                  exams, fees, and all administrative operations.
                </p>
              </div>
            </div>

            {/* UEST - Tutor Finder App */}
            <div className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 border border-orange-100">
              <div className="h-48 bg-gray-100 flex items-center justify-center">
                <img
                  src="/uestlogo.png"
                  alt="UEST Tutor Finder App"
                  className="h-32 object-contain"
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">UEST</h3>
                <p className="text-orange-600 font-medium mb-4">Tutor Finder App</p>
                <p className="text-gray-600">
                  Connect with the best tutors in all fields - from academics to music,
                  languages, and professional skills. Find your perfect learning match.
                </p>
              </div>
            </div>

            {/* UWhiz - Daily Quiz */}
            <div className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 border border-orange-100">
              <div className="h-48 bg-gray-100 flex items-center justify-center">
                <img
                  src="/uwhiz-logo.png"
                  alt="UWhiz Daily Quiz"
                  className="h-32 object-contain"
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">UWhiz</h3>
                <p className="text-orange-600 font-medium mb-4">Daily Quiz Platform</p>
                <p className="text-gray-600">
                  Engaging quiz application with daily challenges, knowledge tests,
                  and competitive learning experiences.
                </p>
              </div>
            </div>
          </div>

          <div className="text-center mt-16">
            <button className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-8 rounded-full transition-colors duration-300 shadow-md hover:shadow-lg">
              Explore All Products
            </button>
          </div>
        </div>
      </section>

      {/* App Download Section - Large with Official Icons */}
<section className="py-28 px-8 bg-white">
  <div className="max-w-6xl mx-auto bg-gradient-to-r from-orange-50 to-orange-100 rounded-[2rem] p-12 shadow-2xl">
    <div className="flex flex-col lg:flex-row items-center gap-16">
      {/* Text and Download Buttons */}
      <div className="text-center lg:text-left flex-1">
        <h2 className="text-5xl md:text-6xl font-bold text-black mb-8 leading-tight">Download the app now!</h2>
        <p className="text-2xl md:text-3xl text-gray-800 mb-12">Find the best tutors in all fields with the UEST app</p>

        <div className="flex flex-col sm:flex-row gap-8 justify-center lg:justify-start">
          {/* Google Play Button - Official Icon */}
          <a href="#" className="bg-black hover:bg-gray-900 text-white px-10 py-5 rounded-2xl flex items-center justify-center gap-4 transition-all duration-300 transform hover:scale-105">
            <div className="text-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 20.5v-17c0-.59.34-1.11.84-1.35L13.69 12l-9.85 9.85c-.5-.25-.84-.76-.84-1.35m13.81-5.38L6.05 21.34l8.49-8.49 2.27 2.27m3.35-4.31c.34.27.59.69.59 1.19s-.22.9-.57 1.18l-2.29 1.32-2.5-2.5 2.5-2.5 2.27 1.31M6.05 2.66l10.76 6.22-2.27 2.27-8.49-8.49z"/>
              </svg>
            </div>
            <div className="text-left">
              <div className="text-xl font-medium tracking-wide">GET IT ON</div>
              <div className="text-2xl font-bold">Google Play</div>
            </div>
          </a>

          {/* App Store Button */}
          <a href="#" className="bg-black hover:bg-gray-900 text-white px-10 py-5 rounded-2xl flex items-center justify-center gap-4 transition-all duration-300 transform hover:scale-105">
            <div className="text-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z" />
              </svg>
            </div>
            <div className="text-left">
              <div className="text-xl font-medium tracking-wide">Download on the</div>
              <div className="text-2xl font-bold">App Store</div>
            </div>
          </a>
        </div>
      </div>

      {/* QR Code Section */}
      <div className="flex flex-col items-center">
        <div className="bg-white p-6 rounded-2xl shadow-xl border-2 border-gray-200">
          {/* Replace with your actual QR code image */}
          <div className="w-48 h-48 bg-gray-100 flex items-center justify-center">
            <span className="text-gray-500 text-lg font-medium">QR Code</span>
          </div>
        </div>
        <p className="text-xl text-gray-800 mt-6 font-semibold">Scan the QR code to download the app</p>
      </div>
    </div>
  </div>
</section>
      {/* Leadership Team Section */}
      <section id="team" className="bg-white py-20 px-6">
        <h2 className="text-4xl font-bold text-orange-500 text-center mb-12">Founder and Chairman of Uest</h2>

        {/* Chairman */}
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row items-center gap-8 bg-orange-50 p-6 rounded-xl shadow-md border border-orange-200 mb-12">
          <img
            src="/c5078ec7b5679976947d90e4a19e1bbb.jpg"
            alt="Chairman"
            className="w-full md:w-60 h-auto object-cover rounded-lg shadow-lg"
          />
          <div className="text-left">
            <h3 className="text-2xl font-bold text-black mb-1">Mr. Khodaji Padsumbiya</h3>
            <p className="text-orange-600 font-medium mb-2">Founder & Chairman</p>
            <p className="text-gray-700 leading-relaxed">
              As the visionary behind UEST, Mr. Khodaji Padsumbiya laid the foundation for transforming education
              through innovation. With bold leadership and a passion for change, He guides UEST towards global impact.
            </p>
            <div className="flex space-x-3 mt-4">
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                </svg>
              </a>
              <a href="#" className="text-gray-600 hover:text-pink-600 transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-500 transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* CTO */}
        <h2 className="text-4xl font-bold text-orange-500 text-center mb-12">CTO of Uest</h2>
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row-reverse items-center gap-8 bg-orange-50 p-6 rounded-xl shadow-md border border-orange-200">
          <img
            src="/c5078ec7b5679976947d90e4a19e1bbb.jpg"
            alt="CTO"
            className="w-full md:w-60 h-auto object-cover rounded-lg shadow-lg"
          />
          <div className="text-left">
            <h3 className="text-2xl font-bold text-black mb-1">Mr. Vivek Moradiya</h3>
            <p className="text-orange-600 font-medium mb-2">Chief Technology Officer</p>
            <p className="text-gray-700 leading-relaxed">
              With a strong technical background and strategic thinking, he ensures our platform remains secure,
              scalable, and cutting-edge — leading the tech that powers UEST's mission.
            </p>
            <div className="flex space-x-3 mt-4">
              <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                </svg>
              </a>
              <a href="#" className="text-gray-600 hover:text-pink-600 transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
              </a>
              <a href="#" className="text-gray-600 hover:text-blue-500 transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* Team Leaders */}
        <h2 className="text-4xl font-bold text-orange-500 text-center mb-12 mt-15">Team Leaders</h2>
        <div className="max-w-5xl mx-auto mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          {[
            {
              name: "Mr. Milan Bhimani",
              title: "Team Leader - UI/UX",
              image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
              description: "Leading frontend innovation and user-centered design.",
            },
            {
              name: "Ms. Ishva Visavadiya",
              title: "Team Leader - Backend",
              image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
              description: "Architecting scalable and secure server-side systems.",
            },
            {
              name: "Mr. Harshad Tank",
              title: "Team Leader - Mobile Dev",
              image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
              description: "Developing fast and modern mobile experiences.",
            },
          ].map((leader, index) => (
            <div
              key={index}
              className="bg-orange-50 p-4 rounded-xl border border-orange-200 shadow-md flex flex-col items-center"
            >
              <img
                src={leader.image}
                alt={leader.name}
                className="w-28 h-28 object-cover rounded-full shadow-lg mb-4"
              />
              <h4 className="text-lg font-semibold text-black">{leader.name}</h4>
              <p className="text-orange-600 font-medium">{leader.title}</p>
              <p className="text-gray-700 text-sm mt-2">{leader.description}</p>
              <div className="flex space-x-3 mt-4">
                <a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                  </svg>
                </a>
                <a href="#" className="text-gray-600 hover:text-pink-600 transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                  </svg>
                </a>
                <a href="#" className="text-gray-600 hover:text-blue-500 transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                  </svg>
                </a>
              </div>
            </div>
          ))}
        </div>



      </section>
      {/* Development Team Section */}
      <section className="bg-white py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-orange-500 text-center mb-16">Development Team</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {[
              {
                name: "Mr. Nikhil Faldu",
                role: "Frontend Developer",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                skills: "React, Next.js, UI/UX"
              },
              {
                name: "Mr. Sham Rathod",
                role: "Backend Developer",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                skills: "Node.js, MongoDB, APIs"
              },
              {
                name: "Ms. Jency Fultariya",
                role: "Mobile Developer",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                skills: "React Native, Flutter"
              },
              {
                name: "Ms. Drashti Khalpada",
                role: "UI/UX Designer",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                skills: "Figma, Adobe XD, Prototyping"
              },
              {
                name: "Ms. Kruti Dasadiya",
                role: "Quality Assurance",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                skills: "Testing, Automation"
              },
              {
                name: "Ms. Dhara Maru",
                role: "DevOps Engineer",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                skills: "AWS, CI/CD, Docker"
              }
            ].map((member, index) => (
              <div
                key={index}
                className="bg-orange-50 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300 border border-orange-100 flex flex-col items-center text-center"
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 object-cover rounded-full mb-4 border-2 border-orange-200"
                />
                <h3 className="text-lg font-bold text-gray-800">{member.name}</h3>
                <p className="text-orange-600 font-medium mb-2">{member.role}</p>
                <p className="text-gray-600 text-sm">{member.skills}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Marketing Team Section */}
      <section className="bg-white py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-orange-500 text-center mb-16">Marketing Team</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {[
              {
                name: "Ms. Dhruvisha Gosai",
                role: "Marketing Head",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                description: "Leading our marketing strategies and brand development initiative"
              },
              {
                name: "Ms. Dhruvi Sapovadiya",
                role: "Digital Marketing Specialist",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                description: "Expert in SEO, social media, and online campaign management."
              },
              {
                name: "Mr. Abhishek Zatakiya",
                role: "Content Strategist",
                image: "/c5078ec7b5679976947d90e4a19e1bbb.jpg",
                description: "Crafting compelling content and communication strategies."
              }
            ].map((member, index) => (
              <div
                key={index}
                className="bg-orange-50 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300 border border-orange-100 flex flex-col items-center text-center"
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 object-cover rounded-full mb-4 border-2 border-orange-200"
                />
                <h3 className="text-lg font-bold text-gray-800">{member.name}</h3>
                <p className="text-orange-600 font-medium mb-2">{member.role}</p>
                <p className="text-gray-600 text-sm">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}