#!/bin/sh

commit_msg_file="$1"
commit_msg=$(cat "$commit_msg_file")

# Allowed types (PascalCase)
allowed_types="Feature|Fix|Update|Refactor|Docs|Style|Test|Chore"

# Check if commit message starts with one of the allowed types
if ! echo "$commit_msg" | grep -Eq "^($allowed_types): "; then
  echo "❌ Invalid commit message format."
  echo "✅ Use one of the following formats:"
  echo "   Feature: Your message"
  echo "   Fix: Your message"
  echo "   Update: Your message"
  echo "   Refactor: Your message"
  echo "   Docs: Your message"
  echo "   Style: Your message"
  echo "   Test: Your message"
  exit 1
fi